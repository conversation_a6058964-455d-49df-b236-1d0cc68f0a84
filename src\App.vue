<!--
 * @Author: error: git config user.name && git config user.email & please set dead value or install git
 * @Date: 2025-05-26 10:01:06
 * @LastEditors: error: git config user.name && git config user.email & please set dead value or install git
 * @LastEditTime: 2025-05-26 10:17:44
 * @FilePath: \cmdt-mobile\src\App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import BasicLayout from './layout/BasicLayout.vue'
import { initFeishu, createMockFeishuSDK } from '@/utils/util'
import { setNavigationBarTitle, navigateBack } from '@/utils/feishu'
import { onMounted } from 'vue'
import { useRoute } from 'vue-router'
import dataStore from '@/stores/datas'
import { storeToRefs } from 'pinia'

const datas = dataStore()
const { showXTModal } = storeToRefs(datas)

const route = useRoute()

// 界面设置
const setUI = async () => {
  // 飞书H5 SDK不需要设置WebView反弹，由飞书客户端自动处理

  // 飞书H5 SDK不需要设置底部偏移量，由飞书客户端自动适配

  // 设置页面标题
  try {
    await setNavigationBarTitle('云图')
  } catch (error) {
    console.error('设置标题失败:', error)
  }

  // 飞书监听返回键 - 使用页面生命周期处理
  const handleBackPress = async () => {
    // 如果是指标详情且组织下探打开状态
    if (route.name === 'IndexInfo' && showXTModal.value) {
      datas.updateShowXTModal(false)
    } else {
      if (history.length <= 1) {
        // 顶级页面，则使用飞书返回API
        try {
          await navigateBack()
        } catch (error) {
          console.error('返回失败:', error)
        }
      } else {
        history.back()
      }
    }
  }

  // 监听浏览器返回事件
  window.addEventListener('popstate', handleBackPress)
}

// 初始化飞书H5 SDK
onMounted(() => {
  console.log('App组件挂载，开始初始化飞书SDK')

  if (!window.h5sdk) {
    console.log('飞书SDK未加载，开始初始化')
    initFeishu(() => {
      console.log('飞书SDK初始化完成，调用setUI')
      setUI()
    })

    // 设置超时备用方案
    setTimeout(() => {
      if (!window.h5sdk) {
        console.warn('飞书SDK加载超时，使用模拟SDK')
        createMockFeishuSDK()
        setUI()
      }
    }, 10000) // 10秒超时

    return
  }

  console.log('飞书SDK已存在，直接调用setUI')
  setUI()
})
</script>

<template>
  <BasicLayout />
</template>

<style>
* {
  -webkit-touch-callout: none; /*系统默认菜单被禁用*/
  -webkit-user-select: none; /*webkit浏览器*/
  -khtml-user-select: none; /*早期浏览器*/
  -moz-user-select: none; /*火狐*/
  -ms-user-select: none; /*IE10*/
  user-select: none;
}
img {
  pointer-events: none;
}
</style>
