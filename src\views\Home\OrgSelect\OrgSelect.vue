<script setup lang="ts">
import { computed } from 'vue'
import datasStore from '@/stores/datas'
import { storeToRefs } from 'pinia'
import { zhMap } from '@/utils/util'

// const completeCheck = new URL(
//   '../../../assets/images/home/<USER>',
//   import.meta.url
// ).href
// const incompleteCheck = new URL(
//   '../../../assets/images/home/<USER>',
//   import.meta.url
// ).href

const props = defineProps<{
  showModal: boolean
}>()

const emits = defineEmits(['cancel', 'save'])

const datas = datasStore()
const { searchForm, orgList, selectedOrgList } = storeToRefs(datas)

// 组织列表
const realOrgList = computed(() => {
  let list = [] as Array<any>
  if (searchForm.value.orgSign) {
    list = orgList.value.filter(
      (item) =>
        item.parentKey ===
        (selectedOrgList.value.length
          ? selectedOrgList.value[selectedOrgList.value.length - 1]
          : 'x00001')
    )
  }

  return list
})

// 面包屑切换组织层级
const toLevel = (key: string) => {
  const index = selectedOrgList.value.indexOf(key)
  datas.updateSelectedOrgList(selectedOrgList.value.slice(0, index))
}

// 组织选择
const orgCheck = (orgFullCode: string) => {
  datas.updateSelectedOrgList([...selectedOrgList.value, orgFullCode])
  datas.updateSearchForm({ ...searchForm.value, orgSign: orgFullCode })
  emits('cancel')
}
</script>
<template>
  <van-popup
    class="org-select-popup"
    :show="props.showModal"
    position="right"
    :style="{ width: '100%', height: '100%' }"
  >
    <!-- 组织区域 -->
    <div class="org-area">
      <div class="_top">
        <template v-for="item in selectedOrgList" :key="item">
          <div class="selected-org" @click="toLevel(item)">
            {{ orgList.filter((z) => z.fullCode === item)[0]?.org }}
          </div>
          <img src="@/assets/images/home/<USER>" alt="" srcset="" />
        </template>

        <div class="selected-org disabled">
          请选择{{ zhMap[selectedOrgList.length + 1] }}级组织
        </div>
      </div>
      <div class="list" v-if="realOrgList.length">
        <div class="item" v-for="item in realOrgList" :key="item.fullCode">
          <div class="_left" @click="orgCheck(item.fullCode)">
            <!-- <img
              :src="
                selectedOrgList[selectedOrgList.length - 1] === item.fullCode
                  ? completeCheck
                  : incompleteCheck
              "
              alt=""
              srcset=""
            /> -->
            <div class="text">
              {{ item.org }}
            </div>
          </div>
          <template v-if="item.list?.length">
            <div
              class="_right"
              @click="
                datas.updateSelectedOrgList([...selectedOrgList, item.fullCode])
              "
            >
              <img
                src="@/assets/images/home/<USER>"
                alt=""
                srcset=""
              />
            </div>
          </template>
          <template v-else>
            <div class="_right">
              <img
                src="@/assets/images/home/<USER>"
                alt=""
                srcset=""
              />
            </div>
          </template>
        </div>
      </div>
      <template v-else>
        <div class="no-org">
          <img src="@/assets/images/home/<USER>" alt="" srcset="" />
          暂无可选组织，请返回上层选择
        </div>
      </template>
      <div class="bottom">
        <div class="cancel" @click="emits('cancel')">取消</div>
      </div>
    </div>
  </van-popup>
</template>
<style lang="less">
.org-select-popup {
  .org-area {
    height: 100vh;
    overflow: hidden;
    ._top {
      overflow-x: auto;
      height: 72px;
      box-sizing: border-box;
      width: 100%;
      display: flex;
      align-items: center;
      padding-left: 20px;
      .selected-org {
        height: 32px;
        mix-blend-mode: normal;
        border-radius: 16px;
        background: #f7f8fa;
        line-height: 32px;
        padding: 0 12px;
        font-size: 13px;
        font-weight: bold;
        color: #13bbad;
        margin-right: 6px;
        white-space: nowrap;
        &.disabled {
          color: #c9cdd4;
        }
      }
      img {
        display: block;
        width: 16px;
        height: 16px;
        margin-right: 6px;
        transform: rotateZ(-90deg);
      }
    }
    .list {
      height: calc(100% - 148px);
      overflow-y: auto;
      box-sizing: border-box;
      padding: 0 20px;
      .item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
        &.hasChild {
          ._left .text {
            color: #13bbad;
          }
        }
        ._left {
          flex: 1;
          background-color: #f7f8fa;
          display: flex;
          align-items: center;
          border-radius: 4px;
          //   img {
          //     display: block;
          //     width: 24px;
          //     height: 24px;
          //     margin-right: 2px;
          //   }
          .text {
            margin-left: 12px;
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #86909c;
            font-family: PingFang SC;
            font-size: 13px;
          }
          margin-right: 12px;
          height: 36px;
          line-height: 36px;
        }
        ._right {
          width: 36px;
          height: 36px;
          mix-blend-mode: normal;
          border-radius: 4px;
          background: #f7f8fa;
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            display: block;
            width: 16px;
            height: 16px;
          }
        }
      }
    }
    .no-org {
      height: calc(100% - 148px);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      img {
        display: block;
        width: 150px;
        height: 150px;
        margin-bottom: 20px;
      }
    }
    .bottom {
      height: 76px;
      display: flex;
      align-items: center;
      justify-content: center;
      & > div {
        &:first-child {
          margin-right: 12px;
          border: 1px solid #00aaa6;
          color: #00aaa6;
          background-color: #fff;
        }
        text-align: center;
        width: 113px;
        height: 44px;
        box-sizing: border-box;
        line-height: 44px;
        color: #fff;
        background-color: #00aaa6;
        font-size: 16px;
        line-height: 44px;
      }
    }
  }
}
</style>
