import { defineStore } from 'pinia'
import type { OrgItem, CompanyItem } from '@/config/org.config'
import type { KeyValues } from '@/config/mutation-types'
import { getYearWeek } from '@/utils/util'
import type { CardItem } from '@/config/card.config'

const year = `${new Date().getFullYear()}`,
  month = `${
    new Date().getMonth() + 1 < 10
      ? '0' + (new Date().getMonth() + 1)
      : new Date().getMonth() + 1
  }`

interface SearchForm {
  signOrgId: string
  orgSign: string
  time: string
  timeType: string
}

const userStore = defineStore('datas', {
  state: () => {
    return {
      searchForm: {
        // 概览页面查询条件
        signOrgId: '',
        orgSign: '',
        time: [year, month].join('-'),
        timeType: 'month'
      } as SearchForm,
      selectedOrgList: [] as Array<string>, // 选中的公司面包屑
      chooseTime: false, // 修改时间
      activeCompany: '', // 当前选中的公司
      requestCompany: false, // 第一次请求公司列表
      companyList: [] as Array<CompanyItem>, // 公司列表
      activePlate: '全部', // 选中的版块
      timeTypeOptions: [
        // 时间类型下拉框
        {
          key: 'month',
          name: '月'
        },
        {
          key: 'week',
          name: '周'
        },
        {
          key: 'day',
          name: '日'
        }
      ],
      orgList: [] as OrgItem[], // 组织机构列表
      cardListLoading: true, // 卡片列表加载状态
      timeTypeOptionInDict: [] as KeyValues[], // 年月日码值
      timeTypeMap: {
        month: '月',
        week: '周',
        day: '日'
      } as Record<string, string>,
      activeIndexQST: '', // 当前激活的趋势图
      activeCardItem: {} as CardItem, // 当前查看的指标详情
      // activeCardItem: {
      //   dmId: 'ZBY00569',
      //   indexId: 'ZBY00569',
      //   indexName: '上线失效率',
      //   indexDt: '2022-11',
      //   fullCode: 'HX-H-H01-H0104-CM_TREE002282',
      //   indexFrequency: '月',
      //   indexFrequencyId: 'M',
      //   org: '平度基地',
      //   orgId: 'CM_TREE002282',
      //   businessSegments: '质量',
      //   businessSegmentsId: 'SEG_82',
      //   signOrgId: 'H0104',
      //   signOrg: null,
      //   actualValue: '1,182.3952',
      //   targetValue: '-',
      //   targetCompletionRate: '',
      //   previousChangeRate: '',
      //   isPreviousRate: 'Y',
      //   isContemRate: 'Y',
      //   contemChangeRate: '',
      //   indexUnitId: 'PPM',
      //   indexType: '反向',
      //   indexNameInd: '上线失效率',
      //   displayIndexName: '上线失效率',
      //   productAtt1Id: 'DIM_603-卡片名称-卡片',
      //   productAtt2Id: '',
      //   productAtt3Id: '',
      //   productAtt4Id: '',
      //   productAtt5Id: '',
      //   productAtt6Id: '',
      //   productAtt7Id: '',
      //   indexSort: 1,
      //   normalWDList: ['DIM_603-卡片名称-卡片-视像', '-', '-', '-'],
      //   wdInCardName: '卡片',
      //   wdInCardTag: [],
      //   cmimId: 'ZBY00569-CM_TREE002282-M-0000-0000-DIM_603-0000-00',
      //   companyName: '智动精工',
      //   label: '同环比恶化,未达标',
      //   pj: 'ZBY00569-CM_TREE002282-M-0000-0000-DIM_603-0000-00-智动精工概览',
      //   recommend: false
      // },
      bkColorMap: {} as Record<string, { bgColor: string; color: string }>, // 版块颜色map
      bkIntroMap: {} as Record<string, string>, // 版块文字简介map
      hichat_accessToken: '', // 信鸿tokne
      showXTModal: false, // 组织下探弹窗展示
      isLandscapeInXT: false // 下探是否横屏共享
    }
  },
  getters: {
    timeType: (state) => state.searchForm.timeType,
    companyName: (state) => {
      return state.companyList.filter(
        (item) => state.activeCompany === item.signOrgId
      )[0].title
    }
  },
  actions: {
    // 更新选中公司
    async updateCompany(signOrgId: string) {
      this.activeCompany = signOrgId
    },
    // 更新searchForm needInitTime 切换timeType时重新设置time
    async updateSearchForm(datas: SearchForm, needInitTime?: boolean) {
      const { timeType } = datas
      let indexDt = datas.time

      if (needInitTime) {
        const year = new Date().getFullYear(),
          month = new Date().getMonth() + 1,
          week = getYearWeek(),
          day = new Date().getDate()
        indexDt =
          timeType === 'month'
            ? `${year}-${month < 10 ? '0' + month : month}`
            : timeType === 'week'
            ? `${year}-${week < 10 ? '0' + week : week}`
            : `${year}-${month < 10 ? '0' + month : month}-${
                day < 10 ? '0' + day : day
              }`
      }
      this.searchForm = {
        ...this.searchForm,
        ...datas,
        time: indexDt
      }
    },
    // 更新选择时间
    async updateChooseTime(value: boolean) {
      this.chooseTime = value
    },
    // 更新请求公司列表
    async updateRequestCompany(value: boolean) {
      this.requestCompany = value
    },
    // 更新公司列表
    async updateCompanyList(list: Array<CompanyItem>) {
      this.companyList = list
    },
    // 更新组织列表
    async updateOrgList(value: Array<OrgItem>) {
      this.orgList = value
    },
    // 更新卡片加载状态
    async updateCardListLoading(loading: boolean) {
      this.cardListLoading = loading
    },
    // 更新选中版块
    async updateActivePlate(value: string) {
      this.activePlate = value
    },
    // 更新码值表
    async updateTimeTypeDict(value: Array<KeyValues>) {
      this.timeTypeOptionInDict = value
    },
    // 更新趋势图
    async updateActiveQST(value: string) {
      this.activeIndexQST = value
    },
    // 更新指标详情
    async updateCardItem(cardItem: CardItem) {
      this.activeCardItem = cardItem
    },
    // 更新信鸿token
    async updateAccessToken(token: string) {
      this.hichat_accessToken = token
    },
    // 更新组织下探展示
    async updateShowXTModal(show: boolean) {
      this.showXTModal = show
    },
    // 更新下探横屏
    async updateIsLandscapeInXT(isLandscapeInXT: boolean) {
      this.isLandscapeInXT = isLandscapeInXT
    },
    // 更新组织选择面包屑
    async updateSelectedOrgList(list: Array<string>) {
      this.selectedOrgList = list
    },
    // 更新版块颜色map
    async updateBKColorMap(
      map: Record<string, { bgColor: string; color: string }>
    ) {
      this.bkColorMap = map
    },
    // 更新版块文字简介map
    async updateBKIntroMap(map: Record<string, string>) {
      this.bkIntroMap = map
    }
  }
})

export default userStore
