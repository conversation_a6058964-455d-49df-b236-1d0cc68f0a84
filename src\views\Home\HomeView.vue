<script setup lang="ts">
import { ref, watch } from 'vue'
import HeaderOperation from './HeaderOperation/HeaderOperation.vue'
import IndexSubscribe from './IndexSubscribe/IndexSubscribe.vue'
import OrgSelect from './OrgSelect/OrgSelect.vue'
import datasStore from '@/stores/datas'
import { storeToRefs } from 'pinia'
import { debounce, dealThousandData } from '@/utils/util'
import { getCardList, sortCardList } from '@/service/home'
import type { CardItem } from '@/config/card.config'
import CardList from '@/components/Card/CardList.vue'
import Decimal from 'decimal.js'
// import { resData } from '@/config/mock-data'

const datas = datasStore()
const {
  timeTypeOptionInDict,
  searchForm,
  timeTypeMap,
  companyList,
  requestCompany,
  orgList
} = storeToRefs(datas)

const showMore = ref(false)

const cardList = ref<Array<CardItem>>([])

const showIndexSubscribe = ref(false)
const showOrgSelect = ref(false)
const headerOperation = ref()

const loadData = debounce(() => {
  datas.updateActivePlate('全部')
  if (
    searchForm.value.orgSign &&
    searchForm.value.signOrgId &&
    searchForm.value.time &&
    searchForm.value.timeType
  ) {
    datas.updateCardListLoading(true)
    getCardList({
      zsbg: '',
      indexDt: searchForm.value.time,
      sign: `${datas.companyName}概览`,
      orgId: searchForm.value.orgSign,
      indexFrequencyId: timeTypeOptionInDict.value.filter(
        (item) => item.value === timeTypeMap.value[searchForm.value.timeType]
      )[0].key
    }).then((res) => {
      // res = resData
      if (Array.isArray(res)) {
        const list = res.map((item: any) => {
          const {
            dmId,
            indexId,
            indexName,
            indexDt,
            fullCode,
            indexFrequency,
            indexFrequencyId,
            org,
            orgId,
            businessSegments,
            businessSegmentsId,
            signOrgId,
            signOrg,
            actualValue,
            targetValue,
            targetCompletionRate,
            previousChangeRate,
            contemChangeRate,
            indexUnitId,
            indexSort,
            productAtt1,
            productAtt2,
            productAtt3,
            productAtt4,
            productAtt1Id,
            productAtt2Id,
            productAtt3Id,
            productAtt4Id,
            productAtt5Id,
            productAtt6Id,
            productAtt7Id,
            precisions,
            indexTypeId,
            indexNameInd,
            cmimId,
            pj // 用于拖拽排序标记
          } = item
          const normalWDList = [
            productAtt1,
            productAtt2,
            productAtt3,
            productAtt4
          ].filter((item) => item && !item.includes('指标卡'))
          let wdInCardName = (
            normalWDList
              .filter((item) => item.includes('卡片名称'))
              .map((item) => item.split('-')[2]) || []
          ).join('-')
          const wdInCardTag = normalWDList
            .filter((item) => item.includes('卡片标签'))
            .map((item) => item.split('-')[2])
          return {
            dmId,
            indexId,
            indexName,
            indexDt,
            fullCode,
            indexFrequency:
              indexFrequency || timeTypeMap.value[searchForm.value.timeType],
            indexFrequencyId,
            org,
            orgId,
            businessSegments,
            businessSegmentsId,
            signOrgId,
            signOrg,
            actualValue: dealThousandData(
              actualValue,
              item.indexUnitId,
              precisions
            ),
            targetValue: dealThousandData(
              targetValue,
              item.indexUnitId,
              precisions
            ),
            targetCompletionRate: targetCompletionRate
              ? `${new Decimal(targetCompletionRate)
                  .mul(new Decimal(100))
                  .toFixed(2, Decimal.ROUND_HALF_UP)}%`
              : '',
            previousChangeRate,
            isPreviousRate: 'Y',
            isContemRate: 'Y',
            contemChangeRate,
            indexUnitId,
            indexType: indexTypeId,
            indexNameInd,
            displayIndexName: indexNameInd || indexName,
            productAtt1Id,
            productAtt2Id,
            productAtt3Id,
            productAtt4Id,
            productAtt5Id,
            productAtt6Id,
            productAtt7Id,
            indexSort,
            normalWDList,
            wdInCardName,
            wdInCardTag,
            cmimId,
            companyName: datas.companyName,
            label: '同环比恶化,未达标',
            pj,
            recommend: false
          }
        })
        cardList.value = list
      } else {
        cardList.value.length = 0
      }
      setTimeout(() => {
        datas.updateCardListLoading(false)
      }, 1000)
    })
  }
}, 800)

// 卡片列表改变
const cardListChange = (list: Array<CardItem>) => {
  cardList.value = list
  saveCardSort()
}

// 保存卡片顺序
const saveCardSort = () => {
  if (cardList.value.length === 0) {
    return
  }
  const postData = cardList.value
    .filter((item) => !item.recommend)
    .map((item, index) => {
      const { pj } = item
      return {
        sort: index + 1,
        val: pj,
        sign: `${datas.companyName}概览`
      }
    })
  sortCardList(postData).then(() => {
    loadData()
  })
}

const indexSubscribeSave = () => {
  showIndexSubscribe.value = false
  headerOperation.value.getOrgList()
  if (orgList.value.length > 0) {
    loadData()
  }
}

watch(searchForm, () => {
  loadData()
})
</script>

<template>
  <!-- 有权限 -->
  <div class="homeview-box" v-show="companyList.length > 0">
    <!-- 顶部背景图 -->
    <div
      class="top-area"
      @click="showMore = !showMore"
      :style="{ '--realheight': showMore ? '254px' : '200px' }"
    >
      <template v-if="showMore">
        <img
          class="img2"
          src="@/assets/images/home/<USER>"
          alt=""
          srcset=""
        />
      </template>
      <template v-else>
        <img
          class="img1"
          src="@/assets/images/home/<USER>"
          alt=""
          srcset=""
        />
        <img
          class="more"
          src="@/assets/images/home/<USER>"
          alt=""
          srcset=""
        />
      </template>
      <img class="bottom-pic" src="@/assets/images/home/<USER>" alt="" />
    </div>
    <!-- 顶部操作栏 -->
    <HeaderOperation
      :showMore="showMore"
      :cardList="cardList"
      @orgClick="showOrgSelect = true"
      ref="headerOperation"
    ></HeaderOperation>
    <!-- 卡片列表 -->
    <div class="card-list">
      <CardList :card-list="cardList" @change="cardListChange"></CardList>
    </div>
    <!-- 订阅指标 -->
    <div class="dyZB" @click="showIndexSubscribe = true">
      <img src="@/assets/images/home/<USER>" alt="" srcset="" />
    </div>
  </div>
  <!-- 指标订阅组件 -->
  <IndexSubscribe
    :show-modal="showIndexSubscribe"
    @cancel="showIndexSubscribe = false"
    @save="indexSubscribeSave"
  />
  <!-- 选择组织组件 -->
  <OrgSelect :show-modal="showOrgSelect" @cancel="showOrgSelect = false" />
  <!-- 无权限显示 -->
  <template v-if="companyList.length === 0 && requestCompany">
    <div class="no-permission">
      <img src="@/assets/images/home/<USER>" alt="" srcset="" />
      暂无数据访问权限，请联系管理员
    </div>
  </template>
</template>
<style lang="less" scoped>
.no-permission {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  img {
    display: flex;
    width: 100px;
    margin-bottom: 20px;
  }
}
.top-area {
  background-color: #13bbad;
  height: var(--realheight);
  width: 100%;
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: -50%;
  z-index: 1;
  img.img1 {
    height: 212px;
  }
  img.img2 {
    height: 266px;
  }
  img {
    display: block;
    width: 100%;
    position: relative;
    z-index: 2;
  }
  img.more {
    width: 16px;
    height: 16px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 64px;
    z-index: 8;
  }
  img.bottom-pic {
    height: 12px;
    position: absolute;
    left: 50%;
    // top: calc(100% - 1px);
    top: 100%;
    z-index: 1;
    margin-left: -50%;
  }
}
.card-list {
  box-sizing: border-box;
  padding: 12px;
}
.dyZB {
  position: fixed;
  bottom: 40px;
  right: 8px;
  height: 56px;
  width: 56px;
  overflow: hidden;
  border-radius: 50% 50%;
  background: linear-gradient(90deg, #48d1cc 0%, #13bbad 100%);
  box-shadow: 0px 4px 6px #0000003d, 0px 0px 6px #0000001f;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  img {
    display: block;
    width: 14px;
    height: 14px;
  }
}
</style>
