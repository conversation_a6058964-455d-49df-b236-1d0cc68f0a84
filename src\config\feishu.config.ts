/**
 * 飞书配置文件
 * 包含飞书H5 SDK的相关配置信息
 */

// 飞书H5 SDK配置
export const FEISHU_CONFIG = {
  // 飞书H5 SDK的CDN地址
  SDK_URL: 'https://lf-scm-cn.feishucdn.com/lark/op/h5-js-sdk-1.5.38.js',

  // 应用配置 - 需要从飞书开放平台获取
  APP_CONFIG: {
    appId: 'cli_a8ad546dfbfa500b', // 应用ID，需要从环境变量或配置中获取
    // 其他配置项可以根据需要添加
  },

  // 需要使用的API列表
  JS_API_LIST: [
    // 导航相关
    'biz.navigation.setTitle',
    'biz.navigation.close',
    'biz.navigation.goBack',

    // 通知相关
    'device.notification.showToast',
    'device.notification.showModal',

    // 聊天相关
    'biz.chat.openSingleChat',
    'biz.chat.openGroupChat',

    // 工具相关
    'biz.util.openLink',
    'biz.util.share',

    // 设备信息
    'device.base.getSystemInfo',

    // 剪贴板
    'biz.clipboard.writeText',
    'biz.clipboard.readText',

    // 图片预览
    'biz.media.previewImage',

    // 文件相关
    'biz.util.downloadFile',
    'biz.util.openDocument'
  ],

  // 错误码映射
  ERROR_CODES: {
    SDK_NOT_READY: 'SDK_NOT_READY',
    API_NOT_AVAILABLE: 'API_NOT_AVAILABLE',
    PERMISSION_DENIED: 'PERMISSION_DENIED',
    NETWORK_ERROR: 'NETWORK_ERROR'
  },

  // 默认配置
  DEFAULT_OPTIONS: {
    timeout: 30000, // 超时时间
    retryCount: 3,  // 重试次数
    retryDelay: 1000 // 重试延迟
  }
}

// 环境配置
export const ENV_CONFIG = {
  // 开发环境
  development: {
    ...FEISHU_CONFIG,
    APP_CONFIG: {
      ...FEISHU_CONFIG.APP_CONFIG,
      appId: import.meta.env.VITE_FEISHU_APP_ID_DEV || '',
    }
  },

  // 测试环境
  test: {
    ...FEISHU_CONFIG,
    APP_CONFIG: {
      ...FEISHU_CONFIG.APP_CONFIG,
      appId: import.meta.env.VITE_FEISHU_APP_ID_TEST || '',
    }
  },

  // 生产环境
  production: {
    ...FEISHU_CONFIG,
    APP_CONFIG: {
      ...FEISHU_CONFIG.APP_CONFIG,
      appId: import.meta.env.VITE_FEISHU_APP_ID_PROD || '',
    }
  }
}

// 获取当前环境的配置
export const getCurrentConfig = () => {
  const env = import.meta.env.MODE || 'development'
  return ENV_CONFIG[env as keyof typeof ENV_CONFIG] || ENV_CONFIG.development
}

// 飞书API权限配置
export const FEISHU_PERMISSIONS = {
  // 基础权限
  BASIC: [
    'biz.navigation.setTitle',
    'device.notification.showToast',
    'device.base.getSystemInfo'
  ],

  // 聊天权限
  CHAT: [
    'biz.chat.openSingleChat',
    'biz.chat.openGroupChat'
  ],

  // 文件权限
  FILE: [
    'biz.util.downloadFile',
    'biz.util.openDocument',
    'biz.media.previewImage'
  ],

  // 剪贴板权限
  CLIPBOARD: [
    'biz.clipboard.writeText',
    'biz.clipboard.readText'
  ]
}

// 飞书API兼容性配置
export const FEISHU_COMPATIBILITY = {
  // 最低支持版本
  MIN_VERSION: {
    android: '3.44.0',
    ios: '3.44.0',
    pc: '3.47.0',
    harmony: '7.35.0'
  },

  // API兼容性映射
  API_COMPATIBILITY: {
    'biz.navigation.setTitle': {
      android: '3.44.0',
      ios: '3.44.0',
      pc: '3.47.0',
      harmony: '7.35.0'
    },
    'biz.chat.openSingleChat': {
      android: '4.0.0',
      ios: '4.0.0',
      pc: '4.0.0',
      harmony: '7.35.0'
    }
    // 可以根据需要添加更多API的兼容性信息
  }
}

// 飞书错误处理配置
export const FEISHU_ERROR_CONFIG = {
  // 是否启用错误上报
  ENABLE_ERROR_REPORT: true,

  // 错误上报的API地址
  ERROR_REPORT_URL: '/api/error/report',

  // 需要上报的错误类型
  REPORTABLE_ERRORS: [
    'SDK_LOAD_FAILED',
    'API_CALL_FAILED',
    'PERMISSION_DENIED'
  ],

  // 错误重试配置
  RETRY_CONFIG: {
    maxRetries: 3,
    retryDelay: 1000,
    exponentialBackoff: true
  }
}
