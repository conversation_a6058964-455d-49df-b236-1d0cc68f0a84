<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { getDimension } from '@/service/home'
import ChooseTime from './ChooseTime.vue'
import type { CardItem } from '@/config/card.config'
import type { CompanyItem } from '@/config/org.config'
import datasStore from '@/stores/datas'
import { showToast } from 'vant'
import { storeToRefs } from 'pinia'
import {
  getBKColorAndIntroMap,
  getTimeType,
  getCompanyList
} from '@/service/home'
// import { resData } from '@/config/mock-data'
import { treeToArray } from '@/utils/util'
import * as _ from 'lodash'
import type { OrgItem } from '@/config/org.config'

const datas = datasStore()
const {
  activeCompany,
  companyList,
  timeTypeOptions,
  searchForm,
  orgList,
  activePlate
} = storeToRefs(datas)

interface Props {
  showMore: boolean // 展示更多
  cardList: Array<CardItem> // 卡片列表
}

const showAction = ref(false) // 展示动作面板
const showTimeTypeAction = ref(false) // 展示时间类型面板

const props = defineProps<Props>()

const emits = defineEmits(['orgClick'])

// 版块列表
const plateList = computed(() => {
  const list = Array.from(
    new Set(props.cardList.map((item) => item.businessSegments))
  )
  return [
    { businessSegments: '全部' },
    ...list.map((item) => {
      return { businessSegments: item }
    })
  ]
})

// 公司选中
const onSelect = (item: any): void => {
  datas.updateCompany(item.signOrgId)
  datas.updateSearchForm({ ...searchForm.value, signOrgId: item.signOrgId })
  datas.updateActivePlate('全部')
  getOrgList()
}

// 去重
const uniqBy = (data: Array<any>): void => {
  const arr = [] as Array<string>
  for (let index = 0; index < data.length; index++) {
    const element = data[index]
    if (arr.includes(element.key)) {
      data.splice(index, 1)
      index--
    } else {
      arr.push(element.key)
    }
  }
}

// 增加parentKey
const addParentKey = (data: Array<any>, parentKey: string) => {
  data.forEach((item: any) => {
    item['parentKey'] = parentKey
    if (Array.isArray(item.list) && item.list.length) {
      addParentKey(item.list, item.fullCode)
    }
  })
}

// 获取组织列表
const getOrgList = (): void => {
  getDimension(`${datas.companyName}`, activeCompany.value).then((res: any) => {
    // let res = resData
    if (Array.isArray(res['组织机构']) && res['组织机构'].length > 0) {
      delFourthOrg(res['组织机构'])
      addParentKey(res['组织机构'], 'x00001')
      // 把去掉四层组织后的数据扁平化后，去重再做处理
      let treeToArrayData = treeToArray(_.cloneDeep(res['组织机构']))
      uniqBy(treeToArrayData)

      datas.updateOrgList(
        treeToArrayData.map((item: any): OrgItem => {
          return {
            org: item.org,
            parentKey: item.parentKey,
            orgId: item.orgId,
            fullCode: item.fullCode,
            list: item.list
          }
        })
      )
      datas.updateSelectedOrgList([orgList.value[0].fullCode])
      datas.updateSearchForm({
        ...searchForm.value,
        orgSign: orgList.value[0].fullCode
      })
    } else {
      // 提示无权限
      showToast('请点击右下角悬浮按钮订阅指标后查看')
      datas.updateOrgList([])
      datas.updateSelectedOrgList([''])
      datas.updateSearchForm({
        ...searchForm.value,
        orgSign: ''
      })
      datas.updateCardListLoading(false)
    }
  })
}

// 版块改变
const plateChange = (name: string) => {
  datas.updateActivePlate(name)
}

// 最高层级下探3层组织
const delFourthOrg = (arr: any, index = 0) => {
  index++
  arr.forEach((item: any) => {
    if (index > 2) {
      item.list = []
    } else {
      delFourthOrg(item.list, index)
    }
  })
}

onMounted(async () => {
  // 获取版块颜色和文字
  getBKColorAndIntroMap().then((res) => {
    const bkColorMap = {} as Record<string, { bgColor: string; color: string }>
    const bkIntroMap = {} as Record<string, string>
    res['smc-bkColorMap'].forEach((element: { key: string; value: string }) => {
      bkColorMap[element.key] = {
        bgColor: element.value.split('~')[0],
        color: element.value.split('~')[1]
      }
    })
    res['smc-bkIntroMap'].forEach((element: { key: string; value: string }) => {
      bkIntroMap[element.key] = element.value
    })

    datas.updateBKColorMap(bkColorMap)
    datas.updateBKIntroMap(bkIntroMap)
  })
  // 获取码值表中的年月日码值
  getTimeType().then((res) => {
    const arr = (res.rows || []).filter(
      (item: any) => item.remarks === '指标频次'
    )
    datas.updateTimeTypeDict(
      arr
        ? arr.map((item: any) => {
            return {
              key: item.id,
              value: item.val
            }
          })
        : []
    )
  })
  let orgList: Array<CompanyItem> = [] // 定义公司列表
  // 获取公司列表
  try {
    let res = await getCompanyList()
    orgList = res.map((item: { key: string; value: string }) => {
      let title =
        item.key === 'H'
          ? item.value
          : item.value.replace(/海信/, '').replace(/公司/, '')
      return {
        title,
        name: item.value,
        signOrgId: item.key
      }
    })
    // 请求公司列表后更新状态
    datas.updateRequestCompany(true)
    if (orgList.length) {
      datas.updateCompanyList(orgList).then(() => {
        // 默认选中第一个公司并获取版块
        datas.updateCompany(companyList.value[0].signOrgId)
        datas.updateSearchForm({
          ...searchForm.value,
          signOrgId: companyList.value[0].signOrgId
        })
        // 获取组织机构列表
        getOrgList()
      })
    }
  } catch (error) {
    console.error(error)
  }
})

defineExpose({
  getOrgList
})
</script>
<template>
  <!-- 引入一个toast组件自动引入样式 -->
  <van-toast></van-toast>
  <div
    class="empty-div"
    :style="{ height: props.showMore ? '210px' : '92px' }"
  ></div>
  <div class="header-operation">
    <div class="top" @click.self="showAction = true">
      {{
        activeCompany
          ? companyList.filter((item) => item.signOrgId === activeCompany)[0]
              .title
          : ''
      }}核心KPI概览
      <img src="@/assets/images/home/<USER>" alt="" srcset="" />
    </div>
    <van-sticky>
      <div class="bottom-area">
        <div class="_top">
          <van-tabs v-model:active="activePlate" shrink @change="plateChange">
            <van-tab
              v-for="(item, index) in plateList"
              :title="item.businessSegments"
              :key="index"
              :name="item.businessSegments"
            >
            </van-tab>
          </van-tabs>
        </div>
        <div class="_bottom">
          <div class="time-type" @click="showTimeTypeAction = true">
            {{
              timeTypeOptions.filter(
                (item) => item.key === searchForm.timeType
              )[0].name
            }}度
            <img src="@/assets/images/home/<USER>" alt="" srcset="" />
          </div>
          <div class="time" @click="datas.updateChooseTime(true)">
            {{ searchForm.time
            }}{{
              timeTypeOptions.filter(
                (item) => item.key === searchForm.timeType
              )[0].name
            }}
            <img src="@/assets/images/home/<USER>" alt="" srcset="" />
          </div>
          <div class="org" v-if="orgList.length > 0" @click="emits('orgClick')">
            <span>{{
              searchForm.orgSign
                ? orgList.filter(
                    (item) => item.fullCode === searchForm.orgSign
                  )[0].org
                : ''
            }}</span>
            <img src="@/assets/images/home/<USER>" alt="" srcset="" />
          </div>
        </div>
      </div>
    </van-sticky>
  </div>
  <!-- 公司选择面板 -->
  <van-action-sheet
    v-model:show="showAction"
    :actions="companyList"
    @select="onSelect"
    cancel-text="取消"
    description="选择公司"
    close-on-click-action
  />
  <!-- 时间类型选择面板 -->
  <van-action-sheet
    v-model:show="showTimeTypeAction"
    :actions="timeTypeOptions"
    @select="(item: any) => (datas.updateSearchForm({...searchForm, timeType : item.key}, true))"
    cancel-text="取消"
    description="选择时间类型"
    close-on-click-action
  />
  <!-- 选择时间 -->
  <ChooseTime></ChooseTime>
</template>
<style lang="less" scoped>
.header-operation {
  height: 144px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 1);
  margin: 0 12px;
  overflow: hidden;
  position: relative;
  z-index: 5;
  .top {
    display: flex;
    align-items: center;
    height: 48px;
    box-sizing: border-box;
    border-bottom: 0.5px solid rgba(229, 230, 235, 1);
    padding: 0 20px;
    color: rgba(29, 33, 41, 1);
    // font-weight: 500;
    font-size: 16px;
    img {
      display: block;
      width: 16px;
      height: 16px;
    }
  }
  .bottom-area {
    position: relative;
    box-sizing: border-box;
    height: 96px;
    // padding: 0 calc((100vw - 351px) / 2);
    // left: 50%;
    // width: 100vw;
    // margin-left: -50vw;
    z-index: 87;
    ._top,
    ._bottom {
      position: absolute;
      left: 50%;
      width: 100vw;
      margin-left: -50vw;
      padding: 0 calc((100vw - 351px) / 2);
      box-sizing: border-box;
      background: #fff;
    }
    ._top {
      top: 0;
      border-bottom: 0.5px solid rgb(229, 230, 235);
      :deep(.van-tabs) {
        border-bottom: 0.5px solid rgb(229, 230, 235);
        .van-tabs__wrap {
          height: 35.5px;
          .van-tabs__nav {
            padding: 0 8px;
          }
          .van-tab {
            font-size: 13px;
            color: rgb(29, 33, 41);
            line-height: 35.5px;
            margin: 0 8px;
            &.van-tab--active {
              font-weight: normal;
              color: rgb(19, 187, 173);
            }
            &.van-tab--grow {
              padding: 0 16px;
            }
          }
          .van-tabs__line {
            background: rgb(19, 187, 173);
            border-radius: 0;
            bottom: 0;
          }
        }
      }
    }
    ._bottom {
      bottom: 0;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      padding: 0 20px;
      height: 60px;
      box-shadow: 0px -0.5px rgba(229, 230, 235, 1) inset;
      & > div {
        color: rgba(78, 89, 105, 1);
        font-family: PingFang SC;
        font-size: 12px;
        padding: 0 12px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 0.5px solid rgba(229, 230, 235, 1);
        border-radius: 17px;
        &.org {
          border-width: 0;
        }
        span {
          display: block;
          max-width: 113px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        img {
          display: block;
          width: 12px;
          height: 12px;
          margin-left: 4px;
        }
        &:not(:last-child) {
          margin-right: 8px;
        }
      }
    }
  }
}
</style>
