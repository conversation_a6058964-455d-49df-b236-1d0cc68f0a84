/*
 * @Description: 生成路由
 * @Author: y<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-09-29 10:43:12
 * @LastEditors: Jhin.Yu <EMAIL>
 * @LastEditTime: 2023-02-17 09:28:11
 */

import type { RouteRecordRaw } from 'vue-router'

import type { Route } from '@/config/route.config'

import { setDocumentTitle, domTitle } from '@/utils/domUtil'

const modules = import.meta.glob(`../views/**/*.vue`)

export function generateRouter(menuList: Array<Route>): Array<RouteRecordRaw> {
  const routes: Array<RouteRecordRaw> = []
  menuList.forEach((item: Route) => {
    const routerItem: RouteRecordRaw = {
      path: item.path,
      name: item.name,
      component: modules[`../${item.filePath}`],
      beforeEnter() {
        setDocumentTitle(`${item.title} - ${domTitle}`)
      },
      children: [],
      meta: {
        keepAlive: item.keepAlive
      }
    }
    if (item.children && item.children.length) {
      routerItem.children = generateRouter(item.children)
    }
    routes.push(routerItem)
  })
  return routes
}
