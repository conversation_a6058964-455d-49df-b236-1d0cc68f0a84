import { ACCESS_TOKEN } from '@/config/mutation-types'
import { getHiChatToken } from '@/service/api'
import { getUrlParam, CooieTool } from '@/utils/util'
import router from './router'
import userStore from '@/stores/user'
import { storeToRefs } from 'pinia'

router.beforeEach((to, from, next) => {
  const ticket = getUrlParam('ticket')
  const user = userStore()
  const { roles } = storeToRefs(user)

  if (CooieTool.getCookie(ACCESS_TOKEN)) {
    if (roles.value.length === 0) {
      // 获取用户信息
      user.GetInfo().then(() => {
        next()
      })
    } else {
      next()
    }
  } else {
    if (ticket) {
      getHiChatToken(
        import.meta.env.VITE_APP_HiChatX_ID,
        import.meta.env.VITE_APP_HiChatX_SECRET
      ).then((res) => {
        if (typeof res === 'string') {
          CooieTool.setCookie(ACCESS_TOKEN, res, 12 * 60 * 60 * 1000) // 有效时间12小时
          next({ ...to })
        }
      })
    }
  }
})
