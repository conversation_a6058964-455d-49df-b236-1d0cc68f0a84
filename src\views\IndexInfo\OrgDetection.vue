<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import datasStore from '@/stores/datas'
import { storeToRefs } from 'pinia'
import { getXTData } from '@/service/info'
import { loopData } from '@/utils/util'
import type { XTComItem } from '@/config/card.config'
import DetectionModal from './DetectionModal/DetectionModal.vue'
import XTModal from './DetectionModal/XTModal.vue'

const datas = datasStore()
const { activeCardItem, showXTModal } = storeToRefs(datas)

const orgXTData = ref<XTComItem>({} as XTComItem) // 组织下探
const xtModal = ref() // 维度下探弹窗

// 获取组织下探数据
const loadXTData = () => {
  const {
    productAtt1Id,
    productAtt2Id,
    productAtt3Id,
    productAtt4Id,
    productAtt5Id,
    productAtt6Id,
    productAtt7Id
  } = activeCardItem.value
  const arr = [
    productAtt1Id,
    productAtt2Id,
    productAtt3Id,
    productAtt4Id,
    productAtt5Id,
    productAtt6Id,
    productAtt7Id
  ] as Array<string | null>
  // arr.forEach((item, index) => {
  //   arr[index] = item
  //     ? item.includes('卡片名称') || item.includes('卡片标签')
  //       ? item
  //       : null
  //     : null
  // })
  const postData = {
    indexId: activeCardItem.value.indexId,
    businessSegmentsId: activeCardItem.value.businessSegmentsId,
    fullCode: activeCardItem.value.fullCode,
    indexDt: activeCardItem.value.indexDt,
    indexFrequencyId: activeCardItem.value.indexFrequencyId,
    cmimId: activeCardItem.value.cmimId,
    productAtt1Id: arr[0],
    productAtt2Id: arr[1],
    productAtt3Id: arr[2],
    productAtt4Id: arr[3],
    productAtt5Id: arr[4],
    productAtt6Id: arr[5],
    productAtt7Id: arr[6]
  }
  getXTData(postData).then((res) => {
    orgXTData.value = loopData(res)[0]
  })
}

const nodeClick = (data: XTComItem) => {
  xtModal.value.initData(data)
}

onMounted(() => {
  loadXTData()
})
</script>
<template>
  <div class="xt">
    <div class="top">
      <div class="title">组织下探</div>
      <div class="btn" @click.stop="datas.updateShowXTModal(true)">分析图</div>
    </div>
    <img
      src="@/assets/images/index-info/<EMAIL>"
      alt=""
      srcset=""
    />
  </div>
  <DetectionModal
    :isOrgXT="true"
    :show="showXTModal"
    :XTData="orgXTData"
    @close="datas.updateShowXTModal(false)"
    @nodeClick="nodeClick"
  />
  <XTModal ref="xtModal" />
</template>
<style lang="less" scoped>
.xt {
  position: relative;
  z-index: 2;
  width: 351px;
  border-radius: 8px;
  background: #ffffff;
  margin: 0 auto;
  box-sizing: border-box;
  margin-bottom: 8px;
  padding: 0;
  .top {
    padding: 12px 12px 0 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    .title {
      font-size: 14px;
      color: #1d2129;
      height: 22px;
      line-height: 22px;
      font-weight: 600;
    }
    .btn {
      color: #13bbad;
    }
  }
  img {
    display: block;
    width: 100%;
  }
}
</style>
