import { defineStore } from 'pinia'
import { getInfo } from '@/service/api'

const userStore = defineStore('user', {
  state: () => {
    return {
      roles: [], // 角色编码数组
      user: {
        name: '',
        org: '',
        email: '',
        tel: '',
        loginName: ''
      },
      searchForm: {
        // 概览页面查询条件
        base: '',
        indexDt: '',
        frequency: ''
      },
      chooseTime: true
    }
  },
  getters: {
    rolesArr: (state) => state.roles,
    nowLoginUser: (state) => state.user.loginName
  },
  actions: {
    /**
     * @description: 获取用户信息
     */
    async GetInfo() {
      // 用户信息
      const userInfo: any = await getInfo()
      this.user = {
        name: userInfo.userName,
        org: userInfo.ldapFullPath,
        email: userInfo.email,
        tel: userInfo.phonenumber,
        loginName: userInfo.loginName
      }
      if (userInfo.roleCodes) {
        this.roles = userInfo.roleCodes
      }
    }
  }
})

export default userStore
