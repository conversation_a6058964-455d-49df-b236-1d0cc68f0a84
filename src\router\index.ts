/*
 * @Author: J<PERSON>.Yu <EMAIL>
 * @Date: 2023-02-16 16:57:42
 * @LastEditors: Jhin.Yu <EMAIL>
 * @LastEditTime: 2023-02-17 15:12:59
 * @FilePath: \cmdt-mobile\src\router\index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import BasicLayout from '@/layout/BasicLayout.vue'

import { routeList } from '@/config/route.config'
import { generateRouter } from './generateRouter'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'index',
    redirect: '/home',
    component: BasicLayout,
    children: generateRouter(routeList)
  }
]

const router = createRouter({
  history: createWebHistory(),
  scrollBehavior() {
    return { top: 0 }
  },
  routes
})

export default router
