/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    CardItem: typeof import('./src/components/Card/CardItem.vue')['default']
    CardList: typeof import('./src/components/Card/CardList.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TrendCom: typeof import('./src/components/Card/TrendCom.vue')['default']
    VanActionSheet: typeof import('vant/es')['ActionSheet']
    VanBadge: typeof import('vant/es')['Badge']
    VanDatePicker: typeof import('vant/es')['DatePicker']
    VanEmpty: typeof import('vant/es')['Empty']
    VanField: typeof import('vant/es')['Field']
    VanLoading: typeof import('vant/es')['Loading']
    VanPicker: typeof import('vant/es')['Picker']
    VanPopup: typeof import('vant/es')['Popup']
    VanSkeleton: typeof import('vant/es')['Skeleton']
    VanSticky: typeof import('vant/es')['Sticky']
    VanTab: typeof import('vant/es')['Tab']
    VanTabs: typeof import('vant/es')['Tabs']
    VanToast: typeof import('vant/es')['Toast']
  }
}
