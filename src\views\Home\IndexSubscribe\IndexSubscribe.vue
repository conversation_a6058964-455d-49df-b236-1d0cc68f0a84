<script lang="ts" setup>
import { watch, ref, computed, reactive, nextTick, defineEmits } from 'vue'
import {
  getIndexTreeListBySignOrg,
  getRoleIndexTreeListBySignOrg,
  saveRoleIndexTree
} from '@/service/home'
import datasStore from '@/stores/datas'
import { storeToRefs } from 'pinia'
import * as _ from 'lodash'
import { arrayToTree, treeToArray, zhMap } from '@/utils/util'
import type { IndexTreeItem } from '@/config/card.config'
import type { TabsInstance } from 'vant'

const completeCheck = new URL(
  '../../../assets/images/home/<USER>',
  import.meta.url
).href
const halfCheck = new URL(
  '../../../assets/images/home/<USER>',
  import.meta.url
).href
const incompleteCheck = new URL(
  '../../../assets/images/home/<USER>',
  import.meta.url
).href

const props = defineProps<{
  showModal: boolean
}>()

const datas = datasStore()
const { searchForm, bkIntroMap } = storeToRefs(datas)

const loadingData = ref(false) // 加载数据
const tabsRef = ref<TabsInstance>()
const activeBK = ref('全部') // 当前选择的版块
const activeIndex = ref('') // 当前选中的指标名称
let activeOrgList = reactive<Array<string>>([]) // 当期选中组织列表 一级组织/二级组织/三级组织/xxxx/xxxx
let ppTreeDataList = reactive<Array<IndexTreeItem>>([]) // 指标树数据列表
// const orgTreeData = ref<Array<IndexTreeItem>>([]) // 指标树数据
let orgCheckedKeysData = reactive<Array<string>>([]) // 公司下组织数据选中
let statusMap = reactive<Record<string, any>>({}) // 状态map

const emits = defineEmits(['cancel', 'save'])

// 版块列表
const businessSegmentsList = computed(() => {
  return [
    ...ppTreeDataList
      .filter((item) => item.parentKey === null)
      .map((item) => {
        return {
          title: item.title,
          name: item.key,
          children: ppTreeDataList.filter((z) => z.parentKey === item.key),
          checkedNums: item.checkedNums || 0
        }
      })
  ]
})

// 指标列表
const indexList = computed(() => {
  return activeBK.value === '全部'
    ? []
    : ppTreeDataList.filter((item) => item.parentKey === activeBK.value)
})

// 组织列表
const orgList = computed(() => {
  let list = [] as Array<IndexTreeItem>
  if (activeBK.value && activeBK.value !== '全部' && activeIndex.value) {
    list = ppTreeDataList.filter(
      (item) =>
        item.parentKey ===
        (activeOrgList.length
          ? activeOrgList[activeOrgList.length - 1]
          : activeIndex.value)
    )
  }
  return list
})

// 获取当前公司下的指标树列表
const getRoleListByCompany = () => {
  return new Promise((resolve): void => {
    getIndexTreeListBySignOrg(
      datas.companyName,
      searchForm.value.signOrgId
    ).then((res) => {
      if (Array.isArray(res) && res.length) {
        console.time('orgInCompany')
        // 数据转树结构后，去掉组织第四层后的数据
        const cOrgTreeData = arrayToTree(res)
        // orgTreeData.value = _.cloneDeep(cOrgTreeData)

        delFourthOrg(cOrgTreeData)
        // 把去掉四层组织后的数据扁平化后，去重再做处理
        let treeToArrayData = treeToArray(_.cloneDeep(cOrgTreeData))
        uniqBy(treeToArrayData)

        treeToArrayData.forEach((item: IndexTreeItem) => {
          statusMap[item.key] = {
            checkedStatus: '',
            checkedNums: 0
          }
        })
        ppTreeDataList.push(...treeToArrayData)
        console.timeEnd('orgInCompany')
      }
      resolve(true)
    })
  })
}

// 去重
const uniqBy = (data: Array<IndexTreeItem>): void => {
  const arr = [] as Array<string>
  for (let index = 0; index < data.length; index++) {
    const element = data[index]
    if (arr.includes(element.key)) {
      data.splice(index, 1)
      index--
    } else {
      arr.push(element.key)
    }
  }
}

// 最高层级下探3层组织
const delFourthOrg = (arr: Array<IndexTreeItem>, index = 0) => {
  index++
  arr.forEach((item: IndexTreeItem) => {
    if (index > 4) {
      item.children = []
    } else {
      if (item.children?.length) {
        delFourthOrg(item.children, index)
      }
    }
  })
}

// 获取当前角色下的选中的组织数据
const getSelectOrgKeysByCompany = () => {
  getRoleIndexTreeListBySignOrg(
    datas.companyName,
    searchForm.value.signOrgId
  ).then((res) => {
    if (Array.isArray(res) && res.length) {
      console.time('roleInCompany')

      const validCheckedKeys: Array<string> = [] // 根据当前权限过滤已勾选有效的组织key
      res.forEach((item: string) => {
        const arr = ppTreeDataList.filter((z: IndexTreeItem) => z.key === item)
        if (arr.length) {
          validCheckedKeys.push(item)
        }
      })
      let realityCheckedKeys: Array<string> = [] // 定义一个实际需要勾选的key值数组
      // 对有效key数组进行循环遍历，从ppTreeDataList中查找每一项的子级，重新组合成实际需要勾选的key数组
      // 为解决当前树组件上下级联动且权限变更导致的前端组件勾选显示问题
      validCheckedKeys.forEach((item: string) => {
        realityCheckedKeys = Array.from(
          new Set([
            ...realityCheckedKeys,
            item,
            ...ppTreeDataList
              .filter((zitem: IndexTreeItem) => zitem.parentKey === item)
              .map((zitem: IndexTreeItem) => zitem.key)
          ])
        )
      })

      orgCheckedKeysData.push(...realityCheckedKeys)
      setStatusMap()

      // 如果实际勾选key数组长度大于有效key数组长度，则证明权限已变更，需要为用户主动保存一次指标订阅卡片
      if (realityCheckedKeys.length > validCheckedKeys.length) {
        // showToast({
        //   message: '当前数据权限已变更，正在为您更新指标卡片订阅列表...',
        //   position: 'top'
        // })
        nextTick(async () => {
          await save(false)
          emits('save')
        })
      }
    }
  })
}

// 标签组件切换
const tabChange = (BKKey: string) => {
  activeBK.value = BKKey
  activeOrgList.length = 0
  nextTick(() => {
    activeIndex.value = BKKey === '全部' ? '' : indexList.value[0].key
  })
}

// 指标切换
const indexChange = (indexKey: string) => {
  activeIndex.value = indexKey
  activeOrgList.length = 0
}

// 面包屑切换组织层级
const toLevel = (key: string) => {
  const index = activeOrgList.indexOf(key)
  activeOrgList.splice(index, activeOrgList.length - index)
}

// 查找父级组织，并根据type进行父级的增减
const findParentKeys = (parentKey: string, type: string) => {
  const currentData = ppTreeDataList.filter((item) => item.key === parentKey)[0]
  // 当前判断数据必须是组织数据
  if (currentData.fullCode) {
    const childKeys = ppTreeDataList
      .filter((item) => item.parentKey === parentKey)
      .map((item) => item.key) // 子级key值数据
    const signArr = childKeys.map((item) => orgCheckedKeysData.indexOf(item))
    const filterSignArr = signArr.filter((item) => item === -1)

    if (filterSignArr.length > 0) {
      if (type === 'reduce') {
        if (orgCheckedKeysData.indexOf(parentKey) !== -1) {
          orgCheckedKeysData.splice(orgCheckedKeysData.indexOf(parentKey), 1)
        }
      }
    } else {
      if (type === 'add') {
        if (orgCheckedKeysData.indexOf(parentKey) === -1) {
          orgCheckedKeysData.push(parentKey)
        }
      }
    }
    findParentKeys(currentData.parentKey, type)
  }
}

// 组织选择
const orgCheck = (orgKey: string) => {
  const selfData = ppTreeDataList.filter((item) => item.key === orgKey)[0] // orgKey数据
  const selfAndChildKeys = treeToArray([selfData]).map(
    (item: IndexTreeItem) => item.key
  ) // orgKey本身及其子级组织key
  if (orgCheckedKeysData.includes(orgKey)) {
    // 如果已勾选，则取消本身及其子级组织的勾选
    selfAndChildKeys.forEach((item: string) => {
      if (orgCheckedKeysData.includes(item)) {
        orgCheckedKeysData.splice(orgCheckedKeysData.indexOf(item), 1)
      }
    })
    findParentKeys(selfData.parentKey, 'reduce')
  } else {
    // 如果未勾选，则勾选本身及其子级组织的勾选
    // orgCheckedKeysData.push(...selfAndChildKeys)
    let cloneData = Array.from(
      new Set(_.cloneDeep([...orgCheckedKeysData, ...selfAndChildKeys]))
    )
    orgCheckedKeysData.length = 0
    orgCheckedKeysData.push(...cloneData)
    findParentKeys(selfData.parentKey, 'add')
  }
}

// 版块勾选
const checkBK = (bkKey: string) => {
  const bkData = businessSegmentsList.value.filter(
    (item) => item.name === bkKey
  )[0]
  const bkOriginData = ppTreeDataList.filter((item) => item.key === bkKey)[0]
  let keyArr = [] as Array<string>
  getChildData([bkOriginData], { keyArr })

  if (bkData.checkedNums > 0) {
    keyArr.forEach((key) => {
      for (let index = 0; index < orgCheckedKeysData.length; index++) {
        const element = orgCheckedKeysData[index]
        if (element === key) {
          orgCheckedKeysData.splice(index, 1)
          break
        }
      }
    })
  } else {
    orgCheckedKeysData.push(...keyArr)
  }
}

// 往下查找查找组织是否有选中，并返回其在orgCheckedKeysData中的下标
const getChildData = (
  child: Array<IndexTreeItem>,
  map: {
    indexArr?: Array<number>
    keyArr?: Array<string>
  }
): void => {
  child.forEach((z: IndexTreeItem) => {
    if (map.indexArr) {
      map.indexArr.push(orgCheckedKeysData.indexOf(z.key))
    }
    if (map.keyArr) {
      map.keyArr.push(z.key)
    }
    if (z.children.length > 0) {
      getChildData(z.children, map)
    }
  })
}

const reset = () => {
  activeBK.value = '全部'
  activeIndex.value = ''
  activeOrgList.length = 0
  ppTreeDataList.length = 0
  orgCheckedKeysData.length = 0
}

const cancelClick = () => {
  reset()
  emits('cancel')
}

// 保存
const save = (needEmit: boolean) => {
  return new Promise((resolve) => {
    let postData = [] as Array<{
      signOrgId: string
      orgId: string
      businessSegmentsId: string
      indexId: string
      fullCode: string | null
    }>
    const checkedNodes = ppTreeDataList.filter((item) => {
      return orgCheckedKeysData.includes(item.key) && item.fullCode !== null
    })
    postData = checkedNodes.map((item) => {
      return {
        signOrgId: item.key.split('-')[0],
        orgId: item.key.split('-')[3],
        businessSegmentsId: item.key.split('-')[1],
        indexId: item.key.split('-')[2],
        fullCode: item.fullCode
      }
    })
    saveRoleIndexTree(datas.companyName, postData).then(() => {
      if (needEmit) {
        reset()
        emits('save')
      }
      resolve(true)
    })
  })
}

const setStatusMap = () => {
  // 拷贝后，按照层级深度倒序排序
  const cloneData = _.cloneDeep(ppTreeDataList).sort(
    (a: IndexTreeItem, b: IndexTreeItem) => {
      if (a.fullCode && b.fullCode) {
        return b.fullCode.split('-').length - a.fullCode.split('-').length
      } else {
        return b.key.length - a.key.length
      }
    }
  )

  cloneData.forEach((item) => {
    const childData = item.children
    if (item.fullCode) {
      // 组织层级
      if (orgCheckedKeysData.includes(item.key)) {
        item.checkedStatus = 'complete'
      } else {
        if (childData.length === 0) {
          item.checkedStatus = 'incomplete'
        } else {
          const filterSignArr = childData
            .map((z) => orgCheckedKeysData.indexOf(z.key))
            .filter((z) => z >= 0)
          if (filterSignArr.length === 0) {
            item.checkedStatus = 'incomplete'
          } else if (filterSignArr.length < childData.length) {
            item.checkedStatus = 'half'
          }
        }
      }
    } else {
      if (item.parentKey) {
        // 指标层级
        let indexArr = [] as Array<number>
        let keyArr = [] as Array<string>
        getChildData(childData, { indexArr, keyArr })
        let filterSignArr = indexArr.filter((z) => z >= 0)
        if (filterSignArr.length >= 1) {
          item.checkedStatus = 'checked'
        } else {
          item.checkedStatus = ''
        }
      } else {
        // 版块层级
        const childKeys = childData.map((z) => z.key)
        let arr = [] as Array<IndexTreeItem>
        childKeys.forEach((z) => {
          arr.push(cloneData.filter((v) => v.key === z)[0])
        })
        item.checkedNums = arr.filter(
          (z) => z.checkedStatus === 'checked'
        ).length
        // 版块层级无法使用statusMap判断
        ppTreeDataList.forEach((z) => {
          if (z.key === item.key) {
            z.checkedNums = arr.filter(
              (z) => z.checkedStatus === 'checked'
            ).length
            // vant-tab组件bug 计算属性businessSegmentsList里每一项的checkedNums已经更改，但组件不会重新渲染，需要调用resize方法进行重绘
            tabsRef.value?.resize()
          }
        })
      }
    }
  })
  reSetStatusMap(cloneData)
}

// setStatusMap设置一次checkedStatus之后，对数据进行二次设置
const reSetStatusMap = (data: Array<IndexTreeItem>) => {
  const cloneData = _.cloneDeep(data)
  cloneData.forEach((item) => {
    const childData: Array<IndexTreeItem> = []
    item.children.forEach((z) => {
      childData.push(cloneData.filter((b) => b.key === z.key)[0])
    })
    if (item.fullCode) {
      // 组织层级
      if (!orgCheckedKeysData.includes(item.key)) {
        if (childData.length !== 0) {
          const statusSignArr = childData.filter(
            (z) => z.checkedStatus === 'half' || z.checkedStatus === 'complete'
          )
          if (statusSignArr.length > 0 && item.checkedStatus === 'incomplete') {
            item.checkedStatus = 'half'
          }
        }
      }
    }
  })
  cloneData.forEach((item) => {
    statusMap[item.key] = {
      checkedStatus: item.checkedStatus,
      checkedNums: item.checkedNums
    }
  })
}

watch(orgCheckedKeysData, () => {
  setStatusMap()
})

watch(
  props,
  async (val) => {
    if (val.showModal) {
      loadingData.value = true
      await getRoleListByCompany()
      loadingData.value = false
      getSelectOrgKeysByCompany()
    }
  },
  {
    immediate: true
  }
)
</script>
<template>
  <van-popup
    class="index-subscribe"
    :show="props.showModal"
    position="right"
    :style="{ width: '100%', height: '100%' }"
  >
    <!-- 顶部tab区域 -->
    <van-tabs
      class="top-tab-arae"
      :class="activeBK !== '全部' ? '' : 'all'"
      v-model:active="activeBK"
      @change="tabChange"
      ref="tabsRef"
    >
      <van-tab
        v-for="item in [
          { title: '全部', name: '全部', checkedNums: 0 },
          ...businessSegmentsList
        ]"
        :key="item.name"
        :name="item.name"
      >
        <template #title>
          <template v-if="item.checkedNums > 0">
            <van-badge :content="item.checkedNums">
              {{ item.title }}
            </van-badge>
          </template>
          <template v-else>{{ item.title }}</template>
        </template>
      </van-tab>
    </van-tabs>
    <!-- 底部操作选择区域 -->
    <div class="bottom-checked-area">
      <template v-if="activeBK === '全部'">
        <!-- 全部版块 -->
        <div class="all-checked">
          <div class="_top">
            <div
              class="item"
              v-for="item in businessSegmentsList"
              :key="item.title"
              @click="tabChange(item.name)"
            >
              <div class="_left">
                <div class="title">{{ item.title }}</div>
                <div class="intro">{{ bkIntroMap[item.title] || '' }}</div>
              </div>
              <div class="_right" @click.stop="checkBK(item.name)">
                <img
                  :src="
                    item.checkedNums === 0
                      ? incompleteCheck
                      : item.checkedNums < item.children.length
                      ? halfCheck
                      : completeCheck
                  "
                  alt=""
                  srcset=""
                />
              </div>
            </div>
          </div>
          <div class="_bottom">
            <div class="cancel" @click="cancelClick">取消</div>
            <div class="save" @click="save(true)">保存</div>
          </div>
        </div>
      </template>
      <!-- 某版块 -->
      <template v-else>
        <div class="single-bk-checked">
          <!-- 指标区域 -->
          <div class="index-area">
            <div
              class="item"
              :class="[
                activeIndex === item.key ? 'active' : '',
                statusMap[item.key].checkedStatus === 'checked' ? 'checked' : ''
              ]"
              v-for="item in indexList"
              :key="item.key"
              @click="indexChange(item.key)"
            >
              <div>
                {{ item.title }}
              </div>
            </div>
          </div>
          <!-- 组织区域 -->
          <div class="org-area">
            <div class="_top">
              <template v-for="item in activeOrgList" :key="item">
                <div class="selected-org" @click="toLevel(item)">
                  {{ ppTreeDataList.filter((z) => z.key === item)[0]?.title }}
                </div>
                <img
                  src="@/assets/images/home/<USER>"
                  alt=""
                  srcset=""
                />
              </template>

              <div class="selected-org disabled">
                请选择{{ zhMap[activeOrgList.length + 1] }}级组织
              </div>
            </div>
            <div class="list">
              <div class="item" v-for="item in orgList" :key="item.key">
                <div class="_left" @click="orgCheck(item.key)">
                  <img
                    :src="
                      statusMap[item.key].checkedStatus === 'complete'
                        ? completeCheck
                        : statusMap[item.key].checkedStatus === 'half'
                        ? halfCheck
                        : incompleteCheck
                    "
                    alt=""
                    srcset=""
                  />
                  <div class="text">
                    {{ item.title }}
                  </div>
                </div>
                <template v-if="item.children?.length">
                  <div class="_right" @click="activeOrgList.push(item.key)">
                    <img
                      src="@/assets/images/home/<USER>"
                      alt=""
                      srcset=""
                    />
                  </div>
                </template>
                <template v-else>
                  <div class="_right">
                    <img
                      src="@/assets/images/home/<USER>"
                      alt=""
                      srcset=""
                    />
                  </div>
                </template>
              </div>
            </div>
            <div class="bottom">
              <div class="cancel" @click="cancelClick">取消</div>
              <div class="save" @click="save(true)">保存</div>
            </div>
          </div>
        </div>
      </template>
    </div>
    <!-- 加载中 -->
    <template v-if="loadingData">
      <div class="fixed-div">
        <van-loading size="24px">加载中...</van-loading>
      </div>
    </template>
  </van-popup>
</template>
<style lang="less">
.fixed-div {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 90;
  height: 100vh;
  width: 100vw;
  background-color: #fff;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}
.index-subscribe {
  background-color: #fff;
  overflow: hidden;
  .top-tab-arae {
    height: 42px;
    &.all {
      .van-tabs__line {
        bottom: 8px !important;
      }
    }
  }
  .bottom-checked-area {
    height: calc(100vh - 42px);
    box-sizing: border-box;
    .all-checked {
      height: 100%;
      padding: 20px 20px 0 20px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      ._top {
        overflow-y: auto;
        height: calc(100% - 76px);
        .item {
          width: 100%;
          margin-bottom: 12px;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          padding: 9px 16px;
          border-radius: 4px;
          background: #f7f8fa;
          ._left {
            max-width: 263px;
            .title {
              color: #4e5969;
              font-family: PingFang SC;
              font-weight: bold;
              font-size: 14px;
              height: 22px;
              line-height: 22px;
            }
            .intro {
              max-width: 263px;
              color: #86909c;
              font-family: PingFang SC;
              font-size: 12px;
              line-height: 20px;
            }
          }
          ._right {
            width: 24px;
            height: 24px;
            img {
              display: block;
              width: 100%;
              height: 100%;
            }
          }
        }
      }
      ._bottom {
        height: 76px;
        display: flex;
        align-items: center;
        justify-content: center;
        & > div {
          &:first-child {
            margin-right: 12px;
            border: 1px solid #00aaa6;
            color: #00aaa6;
            background-color: #fff;
          }
          text-align: center;
          width: 113px;
          height: 44px;
          box-sizing: border-box;
          line-height: 44px;
          color: #fff;
          background-color: #00aaa6;
          font-size: 16px;
          line-height: 44px;
        }
      }
    }
    .single-bk-checked {
      height: 100%;
      overflow: hidden;
      display: flex;
      .index-area {
        width: 97px;
        height: 100%;
        overflow-y: auto;
        background-color: #f7f8fa;
        .item {
          transition: all ease-in-out 0.3s;
          height: 52px;
          box-sizing: border-box;
          padding: 0 16px;
          font-size: 13px;
          color: #4e5969;
          font-family: PingFang SC;
          line-height: 18px;
          display: flex;
          align-items: center;
          position: relative;
          & > div {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
          }
          &::after {
            content: '';
            display: block;
            position: absolute;
            left: 0;
            top: 50%;
            height: 38%;
            transform: translateY(-50%);
            width: 2px;
            border-radius: 0 2px 2px 0;
            background: transparent;
          }
          &.active {
            background-color: #fff;
            font-weight: bold;
            &::after {
              transition: all ease-in-out 0.3s;
              background: #00aaa6;
            }
          }
          &.checked {
            color: #00aaa6;
          }
        }
      }
      .org-area {
        width: calc(100% - 97px);
        overflow: hidden;
        ._top {
          overflow-x: auto;
          height: 72px;
          box-sizing: border-box;
          width: 100%;
          display: flex;
          align-items: center;
          padding-left: 20px;
          .selected-org {
            height: 32px;
            mix-blend-mode: normal;
            border-radius: 16px;
            background: #f7f8fa;
            line-height: 32px;
            padding: 0 12px;
            font-size: 13px;
            font-weight: bold;
            color: #13bbad;
            margin-right: 6px;
            white-space: nowrap;
            &.disabled {
              color: #c9cdd4;
            }
          }
          img {
            display: block;
            width: 16px;
            height: 16px;
            margin-right: 6px;
            transform: rotateZ(-90deg);
          }
        }
        .list {
          height: calc(100% - 148px);
          overflow-y: auto;
          box-sizing: border-box;
          padding: 0 20px;
          .item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
            &.hasChild {
              ._left .text {
                color: #13bbad;
              }
            }
            ._left {
              background-color: #f7f8fa;
              display: flex;
              align-items: center;
              width: 190px;
              border-radius: 4px;
              img {
                display: block;
                width: 24px;
                height: 24px;
                margin-right: 2px;
              }
              .text {
                max-width: 150px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #86909c;
                font-family: PingFang SC;
                font-size: 13px;
              }
              height: 36px;
              line-height: 36px;
            }
            ._right {
              width: 36px;
              height: 36px;
              mix-blend-mode: normal;
              border-radius: 4px;
              background: #f7f8fa;
              display: flex;
              align-items: center;
              justify-content: center;
              img {
                display: block;
                width: 16px;
                height: 16px;
              }
            }
          }
        }
        .bottom {
          height: 76px;
          display: flex;
          align-items: center;
          justify-content: center;
          & > div {
            &:first-child {
              margin-right: 12px;
              border: 1px solid #00aaa6;
              color: #00aaa6;
              background-color: #fff;
            }
            text-align: center;
            width: 113px;
            height: 44px;
            box-sizing: border-box;
            line-height: 44px;
            color: #fff;
            background-color: #00aaa6;
            font-size: 16px;
            line-height: 44px;
          }
        }
      }
    }
  }
  .van-tabs {
    border-bottom: 0.5px solid #e5e6eb;
    .van-tab__text--ellipsis {
      display: block;
      overflow: inherit;
      .van-badge--top-right {
        right: -6px;
      }
    }
    .van-tabs__wrap {
      height: 42px;
      .van-tabs__nav {
        padding-bottom: 0;
        padding-left: 0;
        .van-tab--grow {
          padding: 0 20px;
        }
        .van-tabs__line {
          bottom: 0;
          background: #00aaa6;
          border-radius: 0;
          width: 24px;
        }
        .van-tab {
          color: #1d2129;
          font-family: PingFang SC;
          font-size: 13px;
          &.van-tab--active {
            font-size: 13px;
            font-weight: normal;
            color: #00aaa6;
          }
        }
      }
    }
  }
}
</style>
