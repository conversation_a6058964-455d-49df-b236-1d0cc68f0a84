import requestHttp from '@/utils/requestHttp'

/**
 * @description: 根据信鸿ticket获取pangea token
 * @return {String} pangea token
 */
export async function getHiChatToken(hiChatId: string, appSecret: string) {
  return requestHttp('/api/auth/token/getHiChatToken', {
    method: 'POST',
    body: {
      appId: hiChatId,
      appSecret: appSecret
    }
  })
}

/**
 * @description: 获取用户信息
 * @return {Object} 用户信息对象
 */
export function getInfo() {
  return requestHttp('/api/system/user/info', {})
}
