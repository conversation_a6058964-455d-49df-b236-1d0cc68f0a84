<script lang="ts" setup>
import { ref, computed, defineEmits, onMounted } from 'vue'
import Item from './CardItem.vue'
import type { CardItem } from '@/config/card.config'
import datasStore from '@/stores/datas'
import { storeToRefs } from 'pinia'
import * as _ from 'lodash'
import draggable from 'vuedraggable'

const datas = datasStore()
const { activePlate, cardListLoading } = storeToRefs(datas)

const props = defineProps<{
  cardList: Array<CardItem>
}>()

const drag = ref(false) // 拖拽

const onDragEnd = (data: any) => {
  const { oldIndex, newIndex } = data
  const oldIndexId = cardList.value[oldIndex].pj
  const newIndexId = cardList.value[newIndex].pj
  // 根据版块过滤后的数组排序
  const currRow = cardList.value.splice(oldIndex, 1)[0]
  cardList.value.splice(newIndex, 0, currRow)
  // 全部的数组排序
  let oldIndexInAll = 0
  let newIndexInAll = 0
  const cloneList = _.cloneDeep(props.cardList)
  cloneList.forEach((item: CardItem, index: number) => {
    if (item.pj === oldIndexId) {
      oldIndexInAll = index
    }
    if (item.pj === newIndexId) {
      newIndexInAll = index
    }
  })
  const currRowInAll = cloneList.splice(oldIndexInAll, 1)[0]
  cloneList.splice(newIndexInAll, 0, currRowInAll)
  emits('change', cloneList)
}

const emits = defineEmits(['change'])

// 根据版块过滤
const cardList = computed({
  get: () => {
    return activePlate.value === '全部'
      ? _.cloneDeep(props.cardList)
      : _.cloneDeep(props.cardList).filter(
          (item: CardItem) => item.businessSegments === activePlate.value
        )
  },
  set: () => {
    console.log('dragEnd---->')
  }
})

onMounted(() => {
  // 阻止默认拖拽事件
  document.body.ondrop = function (event) {
    event.preventDefault()
    event.stopPropagation()
  }
})

// 拖拽选项
const dragOptions = computed(() => {
  return {
    animation: 200,
    group: 'description',
    disabled: false,
    ghostClass: 'ghost'
  }
})
</script>
<template>
  <template v-if="cardListLoading">
    <template v-for="i in 5" :key="i">
      <van-skeleton
        style="
          background: #fff;
          height: 166px;
          border-radius: 8px;
          margin-bottom: 12px;
          box-sizing: border-box;
          padding: 0px 24px;
          display: flex;
          align-items: center;
        "
        title
        :row="4"
      />
    </template>
  </template>
  <template v-if="cardList.length > 0">
    <draggable
      v-model="cardList"
      v-bind="dragOptions"
      @start="drag = true"
      @end="onDragEnd"
      item-key="pj"
      handle="._drag"
    >
      <template #item="{ element }">
        <Item :key="element.pj" :data="element"></Item>
      </template>
    </draggable>
  </template>
  <template v-else>
    <div class="no-data">
      <img src="@/assets/images/home/<USER>" alt="" srcset="" />
      暂无数据
    </div>
  </template>
</template>
<style lang="less" scoped>
.no-data {
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 100%;
  margin-top: 100px;
  img {
    display: block;
    width: 100px;
    margin-bottom: 20px;
  }
}
</style>
