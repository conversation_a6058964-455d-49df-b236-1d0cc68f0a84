<script lang="ts" setup>
import { ref } from 'vue'
import DetectionModal from './DetectionModal.vue'
import { getXTData } from '@/service/info'
import type { XTComItem } from '@/config/card.config'
import { loopData } from '@/utils/util'

const show = ref(false) // 展示弹窗
const orgXTItem = ref<XTComItem>({} as XTComItem) // 点击的组织下探数据
const activeWD = ref('') // 当前激活的wd
const wdXTData = ref<XTComItem>({} as XTComItem) // 维度下探

// 初始化数据
const initData = (data: XTComItem) => {
  show.value = true
  orgXTItem.value = data
  lodaWDXTData(data.label.goDown[0])
}

const closeModal = () => {
  show.value = false
}

// 获取维度下探数据
const lodaWDXTData = (wd: string) => {
  wdXTData.value = {} as XTComItem
  activeWD.value = wd
  getXTData({ groupId: orgXTItem.value?.label.groupId, goDown: wd }).then(
    (res: any) => {
      wdXTData.value = loopData(res, wd)[0]
    }
  )
}

defineExpose({
  initData,
  closeModal
})
</script>
<template>
  <!-- 维度下探 -->
  <div class="wdXT-modal" v-if="show" @click.stop="show = false">
    <div class="box" @click.stop="show = true">
      <div class="top">
        <div>
          <div
            v-for="item in orgXTItem?.label.goDown"
            :class="activeWD === item ? 'active' : ''"
            :key="item"
            @click="lodaWDXTData(item)"
          >
            {{ item }}
          </div>
        </div>
      </div>
      <div class="bottom">
        <DetectionModal :isOrgXT="false" :show="show" :XTData="wdXTData" />
      </div>
    </div>
  </div>
</template>
<style lang="less">
.wdXT-modal {
  z-index: 11;
  background-color: rgba(0, 0, 0, 0.4);
  .box {
    width: 100%;
    height: 80vh;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    .top {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 80px;
      & > div {
        margin-right: 20px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        background-color: #f2f3f5;
        border-radius: 4px;
        height: 32px;
        & > div {
          height: 32px;
          padding: 0 14px;
          color: #4e5969;
          font-size: 14px;
          line-height: 32px;
          border-radius: 4px;
          &.active {
            color: #fff;
            background-color: #13bbad;
          }
        }
      }
    }
    .bottom {
      flex: 1;
      .tree-org-node-own-style {
        min-width: 190px;
        .intro {
          display: flex;
          align-items: center;
          padding-bottom: 8px;
          border-bottom: 1px solid #c9cdd4;
          & > div {
            flex: 1;
            white-space: nowrap;
            &:not(:last-child) {
              margin-bottom: 0;
            }
          }
        }
        .thb {
          display: flex;
          align-items: center;
          padding-top: 8px;
          & > div {
            flex: 1;
            white-space: nowrap;
            font-size: 12px;
            color: #4e5969;
            display: flex;
            align-items: center;
            span {
              margin-left: 4px;
              display: flex;
              align-items: center;
            }
            img {
              display: block;
              width: 12px;
              height: 12px;
              margin-right: 2px;
            }
          }
        }
      }
    }
  }
}
</style>
