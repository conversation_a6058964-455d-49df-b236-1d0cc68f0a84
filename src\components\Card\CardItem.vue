<script lang="ts" setup>
import type { CardItem } from '@/config/card.config'
import Trend from './TrendCom.vue'
import datasStore from '@/stores/datas'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'

import Decimal from 'decimal.js'

const datas = datasStore()
const { activeIndexQST, bkColorMap } = storeToRefs(datas)
const router = useRouter()

const caretUp = new URL(
  '../../assets/images/home/<USER>',
  import.meta.url
).href
const caretDown = new URL(
  '../../assets/images/home/<USER>',
  import.meta.url
).href

const props = defineProps<{
  data: CardItem
}>()

// 查看趋势图
const showQST = () => {
  datas.updateActiveQST(
    props.data.pj === activeIndexQST.value ? '' : props.data.pj
  )
}

// 查看指标详情
const toInfo = () => {
  datas.updateCardItem(props.data)
  router.push({
    path: `/indexInfo/${props.data.pj}`
  })
}
</script>
<template>
  <div class="card-item" @click="toInfo">
    <div class="_top">
      <!-- 版块、维度、拖拽 -->
      <div class="plate-wd">
        <div class="_l">
          <span
            :style="{
              backgroundColor:
                bkColorMap[props.data?.businessSegments]?.bgColor ||
                'rgb(218, 245, 236)',
              color:
                bkColorMap[props.data?.businessSegments]?.color ||
                'rgb(0, 186, 128)'
            }"
            >{{ props.data.businessSegments }}</span
          >
          <span
            class="wdTag"
            v-for="(tag, tagIdx) in props.data.wdInCardTag"
            :key="`${tag}${tagIdx}`"
          >
            {{ tag }}
          </span>
        </div>
        <div class="_drag">
          <img src="@/assets/images/home/<USER>" alt="" srcset="" />
        </div>
      </div>
      <!-- 指标名、单位、实际值 -->
      <div class="name-unit">
        <div class="_l">
          <span>
            {{
              (props.data.wdInCardName ? props.data.wdInCardName + ' - ' : '') +
              props.data.displayIndexName
            }}
          </span>
          <span v-if="props.data.indexUnitId">
            ({{ props.data.indexUnitId }})
          </span>
        </div>
        <div class="_r">{{ props.data.actualValue }}</div>
      </div>
      <!-- 目标值、完成率、环比、同比 -->
      <div class="target-wcl">
        <div>
          <span>预定目标</span>
          <span>{{ props.data.targetValue }}</span>
        </div>
        <div>
          <span>实际完成率</span>
          <span>{{ props.data.targetCompletionRate }}</span>
        </div>
        <div>
          <span>指标同比</span>
          <template v-if="props.data.isContemRate === 'Y'">
            <span
              :style="{
                color: props.data.contemChangeRate
                  ? props.data.indexType === '正向'
                    ? props.data.contemChangeRate.includes('-')
                      ? '#0b70fe'
                      : '#f53f3f'
                    : props.data.contemChangeRate.includes('-')
                    ? '#f53f3f'
                    : '#0b70fe'
                  : '#4e5969'
              }"
            >
              <template v-if="props.data.contemChangeRate">
                <img
                  :src="
                    props.data.indexType === '正向'
                      ? props.data.contemChangeRate.includes('-')
                        ? caretDown
                        : caretUp
                      : props.data.contemChangeRate.includes('-')
                      ? caretUp
                      : caretDown
                  "
                  alt=""
                />
              </template>
              {{
                props.data.contemChangeRate
                  ? Math.abs(
                      parseFloat(
                        new Decimal(props.data.contemChangeRate)
                          .mul(new Decimal(100))
                          .toFixed(2, Decimal.ROUND_HALF_UP)
                      )
                    ) + '%'
                  : '-'
              }}</span
            >
          </template>
          <template v-else> 不对比 </template>
        </div>
        <div>
          <span>指标环比</span>
          <template v-if="props.data.isPreviousRate === 'Y'">
            <span
              :style="{
                color: props.data.previousChangeRate
                  ? props.data.indexType === '正向'
                    ? props.data.previousChangeRate.includes('-')
                      ? '#0b70fe'
                      : '#f53f3f'
                    : props.data.previousChangeRate.includes('-')
                    ? '#f53f3f'
                    : '#0b70fe'
                  : '#4e5969'
              }"
            >
              <template v-if="props.data.previousChangeRate">
                <img
                  :src="
                    props.data.indexType === '正向'
                      ? props.data.previousChangeRate.includes('-')
                        ? caretDown
                        : caretUp
                      : props.data.previousChangeRate.includes('-')
                      ? caretUp
                      : caretDown
                  "
                  alt=""
                />
              </template>
              {{
                props.data.previousChangeRate
                  ? Math.abs(
                      parseFloat(
                        new Decimal(props.data.previousChangeRate)
                          .mul(new Decimal(100))
                          .toFixed(2, Decimal.ROUND_HALF_UP)
                      )
                    ) + '%'
                  : '-'
              }}</span
            >
          </template>
          <template v-else> 不对比 </template>
        </div>
      </div>
    </div>
    <div class="_bottom">
      <template v-if="props.data.targetCompletionRate">
        <img
          v-if="parseFloat(props.data.targetCompletionRate) < 100"
          src="@/assets/images/home/<USER>"
          class="_l"
          alt=""
          srcset=""
        />
        <img
          v-else
          src="@/assets/images/home/<USER>"
          class="_l"
          alt=""
          srcset=""
        />
      </template>
      <template v-else>
        <div class="_l"></div>
      </template>
      <div class="_r" @click.stop="showQST">
        <span
          >{{ props.data.pj === activeIndexQST ? '收起' : '查看' }}趋势图</span
        >
        <img
          :class="[props.data.pj === activeIndexQST ? 'active' : '']"
          src="@/assets/images/home/<USER>"
          alt=""
          srcset=""
        />
      </div>
    </div>
    <div
      class="qxt"
      v-if="props.data.pj === activeIndexQST"
      @click.stop="
        () => {
          return false
        }
      "
    >
      <Trend :data="props.data"></Trend>
    </div>
  </div>
</template>
<style lang="less" scoped>
.card-item {
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 12px;
  ._top {
    padding: 16px 20px 11.5px 20px;
    border-bottom: 0.5px solid rgba(229, 230, 235, 1);
    .plate-wd {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 4px;
      ._l {
        display: flex;
        align-items: center;
        span {
          display: block;
          height: 20px;
          line-height: 20px;
          padding: 0 4px;
          font-size: 12px;
          border-radius: 2px;
          background: rgba(242, 243, 245, 1);
          color: #4e5969;
          margin-right: 6px;
        }
      }
      ._drag {
        width: 16px;
        height: 16px;
        img {
          display: block;
          width: 100%;
          height: 100%;
        }
      }
    }
    .name-unit {
      position: relative;
      margin-bottom: 12px;
      ._l {
        height: 24px;
        color: rgba(29, 33, 41, 1);
        font-family: PingFang SC;
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        display: flex;
        align-items: center;
        span {
          display: block;
          &:first-child {
            max-width: 140px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
      ._r {
        position: absolute;
        right: 0;
        bottom: -4px;
        height: 36px;
        mix-blend-mode: normal;
        color: rgba(29, 33, 41, 1);
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 24px;
        line-height: 36px;
      }
    }
    .target-wcl {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      & > div {
        width: 64px;
        white-space: nowrap;
        &:not(:last-child) {
          margin-right: 18px;
        }
        height: 44px;
        display: flex;
        flex-direction: column;
        &:nth-child(1),
        &:nth-child(2) {
          align-items: flex-start;
        }
        &:nth-child(3),
        &:nth-child(4) {
          align-items: flex-end;
        }
        &:nth-child(1) {
          span {
            &:last-child {
              display: block;
              width: 100%;
            }
          }
        }
        span {
          display: block;
          color: #4e5969;
          &:first-child {
            height: 20px;
            line-height: 20px;
            font-size: 12px;
          }
          &:last-child {
            display: flex;
            height: 22px;
            line-height: 22px;
            font-size: 14px;
            font-weight: 500;
            align-items: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            img {
              display: block;
              width: 16px;
              height: 16px;
            }
          }
        }
      }
    }
  }
  ._bottom {
    display: flex;
    padding: 8px 20px;
    align-items: center;
    justify-content: space-between;
    ._l {
      display: block;
      height: 20px;
    }
    ._r {
      display: flex;
      align-items: center;
      height: 20px;
      color: rgba(134, 144, 156, 1);
      font-family: PingFang SC;
      font-size: 12px;
      line-height: 20px;
      img {
        display: block;
        margin-left: 4px;
        height: 16px;
        width: 16px;
        transform: rotateZ(0deg);
        transition: all ease-in-out 0.6s;
        &.active {
          transform: rotateZ(180deg);
        }
      }
    }
  }
  .qxt {
    box-sizing: border-box;
    padding: 0 20px 16px 20px;
  }
}
</style>
