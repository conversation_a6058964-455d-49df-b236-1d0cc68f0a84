export interface Route {
  path: string
  name: string
  filePath: string
  title: string
  children?: Array<Route>
  hidden?: boolean
  keepAlive: boolean
}

export const routeList: Array<Route> = [
  {
    path: 'home',
    name: 'Home',
    filePath: 'views/Home/HomeView.vue',
    title: '首页',
    keepAlive: true
  },
  {
    path: 'indexInfo/:pj',
    name: 'IndexInfo',
    filePath: 'views/IndexInfo/IndexInfo.vue',
    title: '指标详情',
    keepAlive: false
  }
]
