import axios from 'axios'
import { system, ACCESS_TOKEN } from '@/config/mutation-types'
import { CooieTool, encodeUrl, getUrlParam } from '@/utils/util'

async function checkStatus(response: any) {
  if (response.status === 401) {
    CooieTool.delCookie(ACCESS_TOKEN)
    // 弹框提示
  }
}

async function responseJson(response: any) {
  const textTemp = response
  let text
  try {
    text = JSON.parse(textTemp) // 如果返回数据是字符串类型
  } catch (err) {
    text = textTemp
  }
  if (response.status >= 200 && response.status < 300) {
    let notificationType = null // 提示框类型
    if (response.data.code === '0' || response.data.code === 0) {
      // 处理表格数据中的total
      if (
        response.data.data &&
        response.data.data.rows &&
        response.data.data.total
      ) {
        response.data.data.total = parseInt(response.data.data.total)
      }
      // 当alert == 1时进行msg提示
      if (response.data.alert === '1' || response.data.alert === 1) {
        // window.vm.$message.success(
        //   response.data.msg || window.vm.$t("OPERATESUCCESS")
        // );
      }
    } else if (response.data.code && response.data.code.indexOf('E') === 0) {
      // 错误类型
      notificationType = 'error'
    } else if (response.data.code && response.data.code.indexOf('W') === 0) {
      // 警告类型
      notificationType = 'warning'
    } else if (response.data.code && response.data.code.indexOf('N') === 0) {
      // 提示类型
      notificationType = 'info'
    } else {
      return response.data
    }
    return response.data.data
  } else {
    checkStatus(response)
    return {
      code: 1,
      ...response
    }
  }
}

/**
 * Requests a URL, returning a promise.
 *
 * @param  {string} url
 * @param  {object} [options]
 * @return {object}
 */
async function requestHttp(url: string, options?: any) {
  const newOptions = { ...options }
  newOptions.url = url
  newOptions.headers = {}
  // 给所有链接加上systemName
  newOptions.headers['systemName'] = system

  // 处理newOptions
  const finalOptions: any = dealOptions(newOptions)

  return axios(finalOptions.url, finalOptions)
    .then((response) => {
      // 记录当前请求结束时间
      return responseJson(response)
    })
    .catch((error) => {
      console.error(error)
    })
}

function dealOptions(newOptions: any) {
  let dealOption = newOptions
  // 处理请求header
  dealOption = dealHeader(dealOption, newOptions)
  // 处理url
  dealOption = dealUrl(dealOption, newOptions)
  // 转换body
  dealOption.data = dealOption.body
  dealOption.validateStatus = () => true
  return dealOption
}

// 添加header
function dealHeader(dealOption: any, newOptions: any) {
  const newDealOptions = dealOption
  // ticket
  const ticket = getUrlParam('ticket')
  if (ticket) {
    newDealOptions.headers['ticket'] = ticket
  }
  // current-id
  const currentId = CooieTool.getCookie('current-id')
  if (currentId) {
    newDealOptions.headers['current-id'] = currentId
  }
  // 处理header - 权限token
  const token = CooieTool.getCookie(ACCESS_TOKEN)
  if (token && !newOptions.url.includes('/system/sys/info')) {
    newDealOptions.headers['token'] = token
    newDealOptions.headers['Authorization'] = `Bearer ${token}`
  }
  // 当前语言
  newDealOptions.headers['default_language'] = 'zh_CN'
  if (
    newOptions.method === 'POST' ||
    newOptions.method === 'PUT' ||
    newOptions.method === 'DELETE'
  ) {
    newDealOptions.headers = {
      Accept: 'application/json',
      'Content-Type': 'application/json; charset=utf-8',
      'Access-Control-Allow-Origin': '*',
      ...newOptions.headers
    }
  }
  return newDealOptions
}

// 处理url
function dealUrl(dealOption: any, newOptions: any) {
  const requestUrl = newOptions.url
  const newDealOptions = dealOption
  // 处理添加systemName
  if (
    newOptions.method === 'POST' ||
    newOptions.method === 'PUT' ||
    newOptions.method === 'DELETE'
  ) {
    // 转换body
    newDealOptions.body =
      typeof newOptions.body === 'string'
        ? newOptions.body
        : JSON.stringify(newOptions.body)
  }
  // encodeUrl requestUrl
  newDealOptions.url = encodeUrl(requestUrl)
  return newDealOptions
}

export default requestHttp
