<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import datasStore from '@/stores/datas'
import userStore from '@/stores/user'
import { storeToRefs } from 'pinia'
import { getCommentList, getIndexInfo } from '@/service/info'
import type { CardItem } from '@/config/card.config'
import IndexOwner from './IndexOwner.vue'
import OrgDetection from './OrgDetection.vue'
import CommentList from './CommentList.vue'
// import EndPointAnalysis from './EndPointAnalysis.vue'
import type { CommentItem } from '@/config/card.config'

const datas = datasStore()
const user = userStore()
const { activeCardItem, bkColorMap, activeCompany } = storeToRefs(datas)

interface UserItem {
  email: string
  ldapFullPath: string
  loginName: string
  password: string
  phonenumber: string
  userName: string
}

const showMoreInfo = ref(false) // 展示更多指标说明
const indexInfo = ref({
  func: '',
  description: '',
  logic: '',
  frequency: '',
  user: [] as Array<UserItem>
}) // 指标信息
const commentList = ref<Array<CommentItem>>([]) // 评论列表

// 原因分析列表
const loadCommentList = () => {
  const { signOrgId, businessSegmentsId, indexId } = activeCardItem.value
  const postIndexId = `${signOrgId}=${businessSegmentsId}=${indexId}`
  getCommentList(postIndexId).then((res: any) => {
    // res = resData
    // eslint-disable-next-line no-prototype-builtins
    if (res && res.hasOwnProperty('rows') && Array.isArray(res.rows)) {
      commentList.value = res.rows
      commentList.value.forEach((item: CommentItem) => {
        item['isEdit'] = false
      })
    }
  })
}

const loadIndexInfo = () => {
  const postData = {
    indexCode: activeCardItem.value.indexId,
    indexName: activeCardItem.value.indexName,
    menu_name: `${activeCardItem.value.companyName}核心KPI-信鸿轻应用`,
    version: '2.0',
    signOrgId:
      activeCompany.value === 'H'
        ? activeCardItem.value.signOrgId
        : activeCompany.value,
    businessSegmentsId: activeCardItem.value.businessSegmentsId,
    orgId: activeCardItem.value.orgId,
    fullCode: activeCardItem.value.fullCode,
    cmimId: activeCardItem.value.cmimId
  }
  getIndexInfo(postData).then((res) => {
    if (res && res.detail) {
      indexInfo.value.func = res.detail['func']
        ? res.detail['func'].replaceAll('\n', '<br />')
        : ''
      indexInfo.value.description = res.detail['description']
        ? res.detail['description'].replaceAll('\n', '<br />')
        : ''
      indexInfo.value.logic = res.detail['logic']
        ? res.detail['logic'].replaceAll('\n', '<br />')
        : ''
      indexInfo.value.frequency = res.detail['frequency']
    }
    if (Array.isArray(res.user)) {
      indexInfo.value.user = res.user
    }
  })
}

onMounted(() => {
  loadCommentList()
  loadIndexInfo()
})

onUnmounted(() => {
  datas.updateCardItem({} as CardItem)
})
</script>

<template>
  <div class="index-info" :class="showMoreInfo ? 'show-more' : ''">
    <!-- 查看更多遮罩 -->
    <div
      class="zz"
      v-show="showMoreInfo"
      @click.stop="showMoreInfo = false"
    ></div>
    <!-- 指标说明 -->
    <div class="intro" @click.stop="showMoreInfo = false">
      <!-- 指标名称 -->
      <div class="name">
        {{
          (activeCardItem.wdInCardName
            ? activeCardItem.wdInCardName + ' - '
            : '') + activeCardItem.displayIndexName
        }}
      </div>
      <!-- tag标签 -->
      <div class="tag">
        <div
          :style="{
            backgroundColor:
              bkColorMap[activeCardItem?.businessSegments]?.bgColor ||
              'rgb(218, 245, 236)',
            color:
              bkColorMap[activeCardItem?.businessSegments]?.color ||
              'rgb(0, 186, 128)'
          }"
        >
          {{ activeCardItem.businessSegments }}
        </div>
        <div
          v-for="(tag, tagIdx) in activeCardItem.wdInCardTag"
          :key="`${tag}${tagIdx}`"
        >
          {{ tag }}
        </div>
      </div>
      <!-- 定义、公式、来源 -->
      <div class="dyAsAly" :class="showMoreInfo ? 'show-more' : ''">
        <div class="dy">
          <span class="title"
            >指标定义<template v-if="!showMoreInfo">：</template>
          </span>
          <span v-html="indexInfo.description"></span>
        </div>
        <div class="gs">
          <span class="title"
            >计算公式<template v-if="!showMoreInfo">：</template></span
          >
          <span v-html="indexInfo.func"></span>
        </div>
        <div class="ly" v-show="showMoreInfo">
          <span class="title"
            >数据来源<template v-if="!showMoreInfo">：</template></span
          >
          <span v-html="indexInfo.logic"></span>
        </div>
        <!-- 查看更多 -->
        <div
          class="more-btn"
          v-show="!showMoreInfo"
          @click.stop="showMoreInfo = !showMoreInfo"
        >
          更多
        </div>
      </div>
      <!-- 频率 -->
      <div class="pl" v-if="indexInfo.frequency">{{ indexInfo.frequency }}</div>
    </div>
    <!-- 指标主人 -->
    <IndexOwner :user="indexInfo.user" />
    <!-- 下探 -->
    <OrgDetection />
    <!-- 末端因素分析 -->
    <template
      v-if="
        [
          'yuyongjie.ex',
          'yangman2',
          'liuxiang12',
          'wangjunyue',
          'liupingli',
          'zhangchen4',
          'wangshengzhen.ex'
        ].includes(user.nowLoginUser)
      "
    >
      <EndPointAnalysis />
    </template>
    <!-- 评论列表 -->
    <CommentList :comment-list="commentList" @refresh="loadCommentList" />
  </div>
</template>
<style lang="less" scoped>
.index-info {
  min-height: 100vh;
  box-sizing: border-box;
  padding-top: 8px;
  padding-bottom: 50px;
  &::before {
    display: block;
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 100%;
    margin-left: -50%;
    height: 48px;
    background: #13bbad;
    z-index: 1;
  }
  &::after {
    display: block;
    content: '';
    position: absolute;
    top: 48px;
    left: 50%;
    width: 100%;
    margin-left: -50%;
    height: 12px;
    background: url('@/assets/images/home/<USER>') no-repeat center center;
    z-index: 1;
  }
  &.show-more {
    &::after,
    &::before {
      z-index: 4;
    }
    .intro {
      z-index: 5;
    }
    .zz {
      z-index: 3;
    }
  }
  & > * {
    position: relative;
    z-index: 2;
  }
  .intro {
    position: relative;
    width: 351px;
    border-radius: 8px;
    background: #ffffff;
    margin: 0 auto;
    box-sizing: border-box;
    padding: 12px;
    margin-bottom: 8px;
    .name {
      height: 24px;
      color: #1d2129;
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 16px;
      line-height: 24px;
      margin-bottom: 4px;
    }
    .tag {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      & > div {
        &:not(:last-child) {
          margin-right: 6px;
        }
        height: 20px;
        border-radius: 2px;
        background: #f2f3f5;
        line-height: 20px;
        padding: 0 4px;
        font-size: 12px;
        line-height: 20px;
        color: #4e5969;
      }
    }
    .dyAsAly {
      width: 100%;
      box-sizing: border-box;
      padding: 12px;
      background-color: #f2f3f5;
      position: relative;
      .more-btn {
        position: absolute;
        right: 12px;
        bottom: 12px;
        font-size: 12px;
        height: 20px;
        color: #13bbad;
        line-height: 20px;
        background-color: #f2f3f5;
      }
      &.show-more {
        .dy,
        .gs,
        .ly {
          margin-bottom: 12px;
          .title {
            display: block;
            color: #1d2129;
            font-family: PingFang SC;
            font-weight: 600;
            font-size: 14px;
            height: 22px;
            line-height: 22px;
            margin-bottom: 4px;
          }
        }
        .ly {
          margin-bottom: 0;
        }
      }
      &:not(.show-more) {
        .dy,
        .gs,
        .ly {
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3; // 3行
          -webkit-box-orient: vertical;
        }
        .gs {
          -webkit-line-clamp: 1;
        }
      }
      .dy,
      .gs,
      .ly {
        color: #4e5969;
        font-size: 12px;
        line-height: 20px;
        .title {
          display: inline-block;
          color: #86909c;
        }
      }
    }
    .pl {
      margin-top: 8px;
      color: #86909c;
      font-size: 12px;
    }
  }
  .zz {
    width: 100vw;
    height: 100vh;
    position: fixed;
    left: 50%;
    top: 50%;
    margin-left: -50vw;
    margin-top: -50vh;
    z-index: 0;
    background-color: rgba(0, 0, 0, 0.4);
  }
}
</style>
