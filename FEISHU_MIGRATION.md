# 飞书功能迁移指南

本文档说明了如何将原有的 `window.qing.call` 功能迁移到飞书H5 SDK。

## 迁移概述

### 原有功能 vs 飞书功能对照表

| 原有功能 | 飞书H5 SDK 替代方案 | 说明 |
|---------|-------------------|------|
| `setBounce` | 无需设置 | 飞书客户端自动处理WebView反弹 |
| `setWebBottomInset` | 无需设置 | 飞书客户端自动适配底部偏移 |
| `setWebViewTitle` | `tt.setNavigationBarTitle` | 设置导航栏标题 |
| `defback` | `tt.navigateBack` + 事件监听 | 返回键处理 |
| `closeWebView` | `tt.navigateBack` / `tt.exitMiniProgram` | 关闭页面 |
| `chat` | `tt.openChat` | 打开聊天窗口 |
| `rotateUI` | 暂不支持 | 飞书暂不支持强制旋转 |

## 配置步骤

### 1. 环境变量配置

在项目根目录创建环境变量文件：

```bash
# .env.development
VITE_FEISHU_APP_ID_DEV=your_dev_app_id

# .env.test
VITE_FEISHU_APP_ID_TEST=your_test_app_id

# .env.production
VITE_FEISHU_APP_ID_PROD=your_prod_app_id
```

### 2. 飞书开放平台配置

1. 登录 [飞书开放平台](https://open.feishu.cn/)
2. 创建应用并获取 App ID
3. 配置应用权限，确保包含以下权限：
   - 基础信息权限
   - 聊天权限（如需要聊天功能）
   - 导航权限

### 3. 后端鉴权配置

飞书H5 SDK需要后端提供鉴权信息，需要实现以下接口：

```typescript
// 获取飞书鉴权信息的接口
interface FeishuAuthResponse {
  appId: string
  timestamp: number
  nonceStr: string
  signature: string
}
```

## 代码变更说明

### 1. 初始化变更

**原有代码：**
```javascript
import { initQing } from '@/utils/util'

// 初始化信鸿
if (!window.qing) {
  initQing(() => {
    setUI()
  })
}
```

**新代码：**
```javascript
import { initFeishu } from '@/utils/util'

// 初始化飞书
if (!window.h5sdk) {
  initFeishu(() => {
    setUI()
  })
}
```

### 2. API调用变更

**原有代码：**
```javascript
// 设置标题
window.qing.call('setWebViewTitle', { title: '云图' })

// 监听返回键
window.qing.call('defback', {
  success: function () {
    // 处理返回逻辑
  }
})

// 打开聊天
window.qing.call('chat', {
  openId: msgContent.openId,
  draft: msgContent.defaultMessage
})
```

**新代码：**
```javascript
import { setNavigationBarTitle, navigateBack, openChat } from '@/utils/feishu'

// 设置标题
await setNavigationBarTitle('云图')

// 返回处理
await navigateBack()

// 打开聊天
await openChat(msgContent.openId.toString(), msgContent.defaultMessage)
```

### 3. 工具函数使用

项目提供了封装好的飞书工具函数，位于 `src/utils/feishu.ts`：

```javascript
import {
  setNavigationBarTitle,
  showToast,
  navigateBack,
  openChat,
  getSystemInfo,
  setClipboardData,
  getClipboardData,
  showModal,
  previewImage,
  share
} from '@/utils/feishu'
```

## 注意事项

### 1. 兼容性处理

- 保留原有的 `window.qing` 类型定义，确保代码兼容性
- 在飞书环境外，相关功能会降级处理

### 2. 错误处理

- 所有飞书API调用都包含错误处理
- 失败时会在控制台输出详细错误信息
- 可以根据需要添加用户友好的错误提示

### 3. 权限申请

某些API需要用户授权，如：
- 剪贴板访问
- 聊天功能
- 文件访问

### 4. 版本兼容性

不同版本的飞书客户端支持的API可能不同，详见 `src/config/feishu.config.ts` 中的兼容性配置。

## 测试建议

1. **本地测试**：在飞书客户端中打开应用进行测试
2. **功能测试**：逐一测试每个迁移的功能点
3. **兼容性测试**：在不同版本的飞书客户端中测试
4. **降级测试**：在非飞书环境中测试降级逻辑

## 调试工具

### 飞书SDK测试页面

项目提供了一个独立的测试页面 `public/feishu-test.html`，可以用来调试飞书SDK加载问题：

1. 访问 `http://localhost:8003/feishu-test.html`
2. 查看SDK加载状态和错误信息
3. 测试基本的飞书API功能

### 控制台调试

在浏览器控制台中可以看到详细的调试信息：

```javascript
// 检查飞书SDK状态
console.log('h5sdk:', window.h5sdk)
console.log('tt:', window.tt)

// 手动测试API
if (window.tt) {
  window.tt.setNavigationBarTitle({
    title: '测试',
    success: () => console.log('成功'),
    fail: (err) => console.log('失败', err)
  })
}
```

## 常见问题

### Q: 找不到 process 是什么原因？
A: 在 Vite 项目中，应该使用 `import.meta.env` 而不是 `process.env` 来访问环境变量。项目已经修复了这个问题。

### Q: Cannot read properties of undefined (reading 'config') 错误怎么解决？
A: 这个错误表示飞书H5 SDK没有正确加载。解决方案：
1. 检查网络连接，确保能访问飞书SDK的CDN
2. 在飞书客户端中测试，非飞书环境可能无法正常加载
3. 使用 `public/feishu-test.html` 测试页面进行调试
4. 项目已添加模拟SDK作为备用方案

### Q: 飞书SDK加载失败怎么办？
A: 检查网络连接和CDN地址，确保可以正常访问飞书SDK。

### Q: API调用失败怎么办？
A: 检查应用权限配置和鉴权信息是否正确。

### Q: 如何调试飞书API？
A: 在飞书客户端中打开调试模式，查看控制台输出。

### Q: 环境变量配置不生效怎么办？
A: 确保环境变量以 `VITE_` 开头，并且重启开发服务器。

## 后续优化

1. 添加更完善的错误上报机制
2. 实现API调用的重试逻辑
3. 添加性能监控
4. 优化用户体验

## 相关文档

- [飞书开放平台文档](https://open.feishu.cn/document/)
- [飞书H5 SDK文档](https://open.feishu.cn/document/uYjL24iN/uMTMuMTMuMTM/)
- [飞书小程序开发指南](https://open.feishu.cn/document/uYjL24iN/uYjL24iN)
