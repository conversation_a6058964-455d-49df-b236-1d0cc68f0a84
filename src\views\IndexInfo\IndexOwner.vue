<script lang="ts" setup>
import { onMounted } from 'vue'
import { initFeishu } from '@/utils/util'
import { openChat, showToast } from '@/utils/feishu'
import axios from 'axios'
import datasStore from '@/stores/datas'
import { storeToRefs } from 'pinia'
import { requestAccessToken } from '@/service/info'
import Decimal from 'decimal.js'

const datas = datasStore()
const { activeCardItem, hichat_accessToken } = storeToRefs(datas)

interface UserItem {
  email: string
  ldapFullPath: string
  loginName: string
  password: string
  phonenumber: string
  userName: string
}
interface MsgContent {
  openId: number
  defaultMessage: string
}

const props = defineProps<{
  user: Array<UserItem>
}>()

const chat = (userItem: UserItem) => {
  console.log(userItem)
  // 日志埋点
  // request(`/api/smc/SysAnalyseInfo/getXinHongInfo`, {
  //   method: 'POST',
  //   body: {
  //     indexId: `${activeCardItem.value.indexId}==${activeCardItem.value.baseName}`,
  //     menu_name: `${activeCardItem.value.companyName}核心KPI${
  //       this.pageClass === 'indexGeneralViewPage' ? '概览' : '横比'
  //     }-信鸿轻应用`,
  //     communicatePeople: item.userName
  //   }
  // })
  const data = encodeURIComponent(`{array:['${userItem.loginName}']}`)
  axios({
    url: `https://hichatx.hisense.com/gateway/openimport/open/person/getInfoByJobNo?accessToken=${hichat_accessToken.value}`,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    method: 'post',
    data: `eid=101&data=${data}`
  }).then((res: any) => {
    if (res.data?.success) {
      const defaultMessage = `${activeCardItem.value.businessSegments}-${
        activeCardItem.value.wdInCardName
          ? '(' + activeCardItem.value.wdInCardName + ')'
          : ''
      }${activeCardItem.value.displayIndexName}（${
        activeCardItem.value.companyName
      }-${activeCardItem.value.org}-${activeCardItem.value.indexDt}${
        activeCardItem.value.indexFrequency
      }）
目标值：${activeCardItem.value.targetValue} ${
        activeCardItem.value.indexUnitId
      }；实际值：${activeCardItem.value.actualValue} ${
        activeCardItem.value.indexUnitId
      }；完成率：${
        activeCardItem.value.targetCompletionRate
          ? new Decimal(activeCardItem.value.targetCompletionRate)
              .mul(new Decimal(100))
              .toFixed(2, Decimal.ROUND_HALF_UP)
          : '-'
      }%；
${
  activeCardItem.value.isContemRate === 'Y'
    ? '同比' +
      (activeCardItem.value.contemChangeRate
        ? (activeCardItem.value.contemChangeRate.includes('-') &&
            activeCardItem.value.indexType === '反向') ||
          (!activeCardItem.value.contemChangeRate.includes('-') &&
            activeCardItem.value.indexType === '正向')
          ? '上涨'
          : '下降'
        : '') +
      '：' +
      (activeCardItem.value.contemChangeRate
        ? Math.abs(
            parseFloat(
              new Decimal(activeCardItem.value.contemChangeRate)
                .mul(new Decimal(100))
                .toFixed(2, Decimal.ROUND_HALF_UP)
            )
          )
        : '-') +
      '%；'
    : ''
} ${
        activeCardItem.value.isPreviousRate === 'Y'
          ? '环比' +
            (activeCardItem.value.previousChangeRate
              ? (activeCardItem.value.previousChangeRate.includes('-') &&
                  activeCardItem.value.indexType === '反向') ||
                (!activeCardItem.value.previousChangeRate.includes('-') &&
                  activeCardItem.value.indexType === '正向')
                ? '上涨'
                : '下降'
              : '') +
            '：' +
            (activeCardItem.value.previousChangeRate
              ? Math.abs(
                  parseFloat(
                    new Decimal(activeCardItem.value.previousChangeRate)
                      .mul(new Decimal(100))
                      .toFixed(2, Decimal.ROUND_HALF_UP)
                  )
                )
              : '-') +
            '%；'
          : ''
      }`
      openFeishuChatWindow({
        openId: res.data.data[0],
        defaultMessage
      })
    }
  })
}

const openFeishuChatWindow = async (msgContent: MsgContent) => {
  if (!window.h5sdk) {
    initFeishu(() => {
      openFeishuChatWindow(msgContent)
    })
    return
  }

  try {
    // 使用飞书工具函数打开聊天窗口
    await openChat(msgContent.openId.toString(), msgContent.defaultMessage)
  } catch (error) {
    console.error('打开聊天窗口失败:', error)
    // 如果打开聊天失败，显示错误提示
    try {
      await showToast('无法打开聊天窗口', 'none')
    } catch (toastError) {
      console.error('显示提示失败:', toastError)
    }
  }
}

// 获取信鸿token
const getHichatAccessToken = () => {
  requestAccessToken().then((res) => {
    datas.updateAccessToken(res.accessToken)
  })
}

onMounted(() => {
  getHichatAccessToken()
})
</script>
<template>
  <div class="index-owner">
    <div class="title">指标主人</div>
    <div class="item" v-for="(item, index) in props.user" :key="index">
      <div class="top">
        <div class="left">
          <!-- 职位 -->
          <div class="tag">{{ item.password }}</div>
          <!-- 姓名、电话 -->
          <div class="nameAphone">
            {{ item.userName }} {{ item.phonenumber }}
          </div>
        </div>
        <div class="right">
          <a
            :href="`tel:${item.phonenumber}`"
            style="color: #00aaa6; text-decoration: underline"
            ><img
              src="@/assets/images/index-info/<EMAIL>"
              alt=""
              srcset=""
          /></a>
          <div @click.stop="chat(item)">
            <img
              src="@/assets/images/index-info/<EMAIL>"
              alt=""
              srcset=""
            />
          </div>
        </div>
      </div>
      <!-- 部门 -->
      <div class="bm">{{ item.ldapFullPath }}</div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.index-owner {
  position: relative;
  width: 351px;
  border-radius: 8px;
  background: #ffffff;
  margin: 0 auto;
  box-sizing: border-box;
  padding: 12px;
  margin-bottom: 8px;
  & > .title {
    font-size: 14px;
    color: #1d2129;
    height: 22px;
    line-height: 22px;
    margin-bottom: 12px;
    font-weight: 600;
  }
  .item {
    &:not(:last-child) {
      margin-bottom: 8px;
    }
    width: 327px;
    box-sizing: border-box;
    mix-blend-mode: normal;
    border-radius: 4px;
    border: 1px solid #f2f3f5;
    box-sizing: border-box;
    background: #ffffff;
    padding: 16px 12px 16px 16px;
    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 4px;
      .left {
        .tag {
          display: inline-block;
          height: 20px;
          border-radius: 2px;
          background: #f1fffe;
          line-height: 20px;
          padding: 0 4px;
          color: #13bbad;
          font-size: 12px;
          margin-bottom: 4px;
        }
        .nameAphone {
          color: #1d2129;
          font-size: 14px;
          height: 20px;
          line-height: 20px;
          font-weight: 600;
        }
      }
      .right {
        display: flex;
        align-items: center;
        img {
          display: block;
          width: 32px;
          height: 32px;
          overflow: hidden;
          border-radius: 16px;
        }
        a {
          margin-right: 8px;
        }
      }
    }
    .bm {
      line-height: 20px;
      color: #4e5969;
      font-size: 12px;
    }
  }
}
</style>
