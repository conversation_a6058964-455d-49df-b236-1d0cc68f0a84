{"name": "cmdt-mobile", "version": "0.0.0", "private": true, "scripts": {"dev": "vite --mode development", "build": "run-p type-check build-only", "preview": "vite preview", "build-only": "vite build --mode production", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix", "format": "prettier --write \"./**/*.{html,vue,ts,js,json,md}\""}, "dependencies": {"@types/lodash": "^4.14.191", "amfe-flexible": "^2.2.1", "decimal.js": "^10.4.3", "echarts": "^5.4.1", "eslint": "^8.34.0", "moment": "^2.29.4", "normalize.css": "^8.0.1", "pinia": "^2.0.28", "prettier": "^2.8.4", "vant": "^4.0.11", "vue": "^3.2.45", "vue-router": "^4.1.6", "vue3-tree-org": "^4.2.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@types/node": "^18.11.12", "@types/qs": "^6.9.7", "@typescript-eslint/eslint-plugin": "^5.52.0", "@typescript-eslint/parser": "^5.52.0", "@vitejs/plugin-vue": "^4.0.0", "@vitejs/plugin-vue-jsx": "^3.0.0", "@vue/tsconfig": "^0.1.3", "axios": "^1.3.3", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.9.0", "less": "^4.1.3", "npm-run-all": "^4.1.5", "postcss": "^8.4.21", "postcss-pxtorem": "^6.0.0", "typescript": "~4.7.4", "unplugin-vue-components": "^0.24.0", "vite": "^4.0.0", "vue-tsc": "^1.0.12"}}