<script lang="ts" setup>
import { onMounted, ref, onUnmounted } from 'vue'
import { getOrgEndPoint } from '@/service/info'

import datasStore from '@/stores/datas'
import { storeToRefs } from 'pinia'

const datas = datasStore()
const { activeCardItem } = storeToRefs(datas)

interface orgEndPointItem {
  des?: string
  detail?: Array<string>
}

const orgYSFXList = ref<Array<orgEndPointItem>>([]) // 组织层级下探

const loadOrgEndPointData = () => {
  const {
    productAtt1Id,
    productAtt2Id,
    productAtt3Id,
    productAtt4Id,
    productAtt5Id,
    productAtt6Id,
    productAtt7Id
  } = activeCardItem.value
  const arr = [
    productAtt1Id,
    productAtt2Id,
    productAtt3Id,
    productAtt4Id,
    productAtt5Id,
    productAtt6Id,
    productAtt7Id
  ]
  arr.forEach((item, index) => {
    arr[index] = item
      ? item.includes('卡片名称') || item.includes('卡片标签')
        ? item
        : null
      : null
  })
  const postData = {
    indexName: activeCardItem.value.displayIndexName,
    indexId: activeCardItem.value.indexId,
    businessSegmentsId: activeCardItem.value.businessSegmentsId,
    fullCode: activeCardItem.value.fullCode,
    indexDt: activeCardItem.value.indexDt,
    indexFrequencyId: activeCardItem.value.indexFrequencyId,
    cmimId: activeCardItem.value.cmimId,
    productAtt1Id: arr[0],
    productAtt2Id: arr[1],
    productAtt3Id: arr[2],
    productAtt4Id: arr[3],
    productAtt5Id: arr[4],
    productAtt6Id: arr[5],
    productAtt7Id: arr[6]
  }
  getOrgEndPoint(postData).then((res: any) => {
    if (Array.isArray(res) && res.length) {
      orgYSFXList.value = res
    }
  })
}

onMounted(() => {
  loadOrgEndPointData()
})
onUnmounted(() => {
  orgYSFXList.value = [] as Array<orgEndPointItem>
})
</script>
<template>
  <div class="end-point-analysis" v-if="orgYSFXList.length !== 0">
    <div class="top">
      <div class="title">末端因素分析</div>
    </div>
    <template v-if="orgYSFXList.length">
      <div class="item">
        <div class="title">管理层级</div>
        <div class="intro" v-html="orgYSFXList[0].des"></div>
        <div
          class="reason"
          v-for="(item, index) in orgYSFXList.slice(1, orgYSFXList.length)"
          :key="index"
        >
          <div class="_title" v-html="item.des"></div>
          <div
            class="_intro"
            v-for="(zitem, zindex) in item.detail || []"
            :key="`${zindex}-${index}`"
            v-html="zitem"
          ></div>
        </div>
      </div>
    </template>
  </div>
</template>
<style lang="less" scoped>
.end-point-analysis {
  position: relative;
  z-index: 2;
  width: 351px;
  border-radius: 8px;
  background: #ffffff;
  margin: 0 auto;
  box-sizing: border-box;
  margin-bottom: 8px;
  padding: 0;
  padding-bottom: 12px;
  .top {
    padding: 12px 12px 0 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    .title {
      font-size: 14px;
      color: #1d2129;
      height: 22px;
      line-height: 22px;
      font-weight: 600;
    }
  }
  .item {
    width: 327px;
    margin: 0 auto;
    box-sizing: border-box;
    border-radius: 4px;
    background: #f2f3f5;
    padding: 12px;
    .title {
      height: 22px;
      color: #1d2129;
      font-weight: bold;
      font-size: 14px;
      line-height: 22px;
      position: relative;
      &::before {
        content: '';
        display: block;
        position: absolute;
        left: -12px;
        top: 50%;
        margin-top: -7px;
        height: 14px;
        width: 4px;
        border-radius: 0px 2px 2px 0px;
        background-color: #13bbad;
      }
    }
    .intro {
      height: 20px;
      color: #4e5969;
      font-size: 12px;
      line-height: 20px;
      margin-bottom: 8px;
      ._num {
        color: #0b70fe;
      }
    }
    .reason {
      width: 100%;
      padding-left: 16px;
      font-size: 12px;
      line-height: 20px;
      margin-bottom: 8px;
      ._title {
        color: #4e5969;
        position: relative;
        &::before {
          position: absolute;
          content: '●';
          display: block;
          left: -16px;
          top: 0;
          height: 16px;
          width: 16px;
          text-align: left;
          line-height: 16px;
        }
        ._num {
          color: #0b70fe;
        }
      }
      ._intro {
        color: #1d2129;
        font-weight: bold;
        ._num {
          color: #0b70fe;
        }
      }
    }
  }
}
</style>
