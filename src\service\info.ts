import requestHttp from '@/utils/requestHttp'

// 获取评论列表
export function getCommentList(indexId: string) {
  return requestHttp(`/api/smc/indexComment/list?pageNum=1&pageSize=99999`, {
    method: 'POST',
    body: {
      indexId
    }
  })
}

// 获取指标定义详情
export function getIndexInfo(body: any) {
  return requestHttp(`/api/smc/indexInfo/getIndexDetailByCode`, {
    method: 'POST',
    body
  })
}

// 获取信鸿token
export function requestAccessToken() {
  return requestHttp(`/api/smc/hichatx/getAccessToken`)
}

// 获取组织下探数据
export function getXTData(body: any) {
  return requestHttp(`/api/smc2/newIndexLibrary/orgList`, {
    method: 'POST',
    body
  })
}

// 获取维度下探数据
export function getWDXTData(body: any) {
  return requestHttp(`/api/smc2/newIndexLibrary/dimensionGoDown`, {
    method: 'POST',
    body
  })
}

// 获取末端因素分析（组织层级）
export function getOrgEndPoint(body: any) {
  return requestHttp(`/api/smc2/newIndexLibrary/endFactor`, {
    method: 'POST',
    body
  })
}
