import requestHttp from '@/utils/requestHttp'

// 获取当前登录人权限列表
export function getCompanyList() {
  return requestHttp(`/api/smc2/codeValue/getRelation1`)
}

// 获取字典中版块文本简介和版块颜色
export function getBKColorAndIntroMap() {
  return requestHttp(
    `${decodeURIComponent(
      '/api/system/dict/type/query?types=smc-bkIntroMap%2Csmc-bkColorMap&languageCode=zh_CN'
    )}`
  )
}

/**
 * @description: 根据公司获取版块列表
 */
export function getDimension(companyName: string, signOrgId: string) {
  return requestHttp('/api/smc2/newIndexLibrary/searchDimension', {
    method: 'POST',
    body: {
      sign: `${companyName}概览`,
      signOrgId
    }
  })
}

/**
 * @description: 获取码值表中的年月日码值
 */
export function getTimeType() {
  return requestHttp(
    `/api/smc2/codeValue/searchCodeValue?pageSize=1000&pageNum=1`
  )
}

// 获取卡片列表
export function getCardList(body: any) {
  return requestHttp(`/api/smc2/newIndexLibrary/searchGL`, {
    method: 'POST',
    body
  })
}

// 获取图表数据
export function getChartData(body: any) {
  return requestHttp(`/api/smc2/newIndexLibrary/searchTrend`, {
    method: 'POST',
    body
  })
}

// 卡片列表排序
export function sortCardList(body: any) {
  return requestHttp(`/api/smc2/indexSort/updateSort`, {
    method: 'POST',
    body
  })
}

// 获取当前公司下的指标树数据
export function getIndexTreeListBySignOrg(
  companyName: string,
  signOrgId: string
) {
  return requestHttp(`/api/smc2/newuserIndexRelation/search`, {
    method: 'POST',
    body: {
      sign: `${companyName}概览`,
      signOrgId
    }
  })
}

// 获取当前公司下当前登录人已勾选的组织数据
export function getRoleIndexTreeListBySignOrg(
  companyName: string,
  signOrgId: string
) {
  return requestHttp(`/api/smc2/newuserIndexRelation/searchRole2`, {
    method: 'POST',
    body: {
      sign: `${companyName}概览`,
      signOrgId
    }
  })
}

// 保存指标勾选
export function saveRoleIndexTree(
  companyName: string,
  postData: Array<{
    signOrgId: string
    orgId: string
    businessSegmentsId: string
    indexId: string
    fullCode: string | null
  }>
) {
  return requestHttp('/api/smc2/newuserIndexRelation/insert', {
    method: 'POST',
    body: {
      sign: `${companyName}概览`,
      listRelation: postData
    }
  })
}

// 提交评论
export function submitComment(body: any) {
  return requestHttp(`/api/smc/indexComment/save`, {
    method: 'POST',
    body
  })
}

// 修改评论
export function updateComment(body: any) {
  return requestHttp(`/api/smc/indexComment/update`, {
    method: 'PUT',
    body
  })
}

// 删除评论
export function delComment(body: any) {
  return requestHttp(`/api/smc/indexComment/remove`, {
    method: 'DELETE',
    body
  })
}
