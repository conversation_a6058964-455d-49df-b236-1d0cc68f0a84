/*
 * @Description:
 * @Author: g<PERSON><PERSON><PERSON>
 * @Date: 2021-04-12 15:46:18
 * @LastEditors: Jhin.Yu <EMAIL>
 * @LastEditTime: 2023-02-17 16:13:52
 */
import type { XTComItem, IndexTreeItem } from '@/config/card.config'
import datasStore from '@/stores/datas'
import Decimal from 'decimal.js'

// 获取urlParams
export function encodeUrl(url: string): string {
  if (url.indexOf('?') === -1) {
    return url
  }
  const path = url.slice(0, url.indexOf('?'))
  const queryStr = url.slice(url.indexOf('?') + 1) // 获取url中"?"符后的字串
  const queryArr = queryStr.split('&')
  let encodeStr = ''
  for (let i = 0; i < queryArr.length; i++) {
    const keyValue = queryArr[i].split('=')
    if (keyValue.length > 1) {
      encodeStr =
        encodeStr +
        `${keyValue[0]}=${
          keyValue[1].startsWith('http%3A') ||
          keyValue[1].startsWith('https%3A')
            ? keyValue[1]
            : encodeURIComponent(keyValue[1])
        }`
      if (i < queryArr.length - 1) {
        encodeStr = encodeStr + '&'
      }
    }
  }
  return `${path}${encodeStr ? '?' : ''}${encodeStr}`
}

// 获取url中的参数 getUrlParam
const getUrlParam = (name: string): string | null => {
  const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i')
  const r = window.location.search.substr(1).match(reg)
  if (r != null) {
    return decodeURI(r[2])
  } else {
    return null
  }
}

/**
 * @description: 处理参数返回&xx=xx&xx=xx
 * @param {*} paramsObj 未处理的对象
 * @return {*} 返回&xx=xx&xx=xx
 */
const buildParams = (paramsObj: any): string => {
  const arr = []
  for (const key in paramsObj) {
    if (Object.hasOwnProperty.call(paramsObj, key)) {
      const element = paramsObj[key]
      if (element !== undefined && element !== null) {
        arr.push(`${key}=${element}`)
      }
    }
  }
  return arr.join('&')
}

//获取cookie、
const CooieTool = {
  getCookie: (name: string) => {
    let arr: string[] | null = []
    const reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)')
    if ((arr = document.cookie.match(reg))) return arr[2]
    else return null
  },

  //设置cookie,增加到vue实例方便全局调用
  setCookie: (c_name: string, value: any, expiredays: number) => {
    const exdate = new Date()
    exdate.setDate(exdate.getDate() + expiredays)
    document.cookie =
      c_name +
      '=' +
      escape(value) +
      (expiredays == null ? '' : ';expires=' + exdate.toUTCString())
  },

  //删除cookie
  delCookie: (name: string) => {
    const exp = new Date()
    exp.setTime(exp.getTime() - 1)
    const cval = CooieTool.getCookie(name)
    if (cval != null)
      document.cookie = name + '=' + cval + ';expires=' + exp.toUTCString()
  }
}

// 初始化飞书H5 SDK
const initFeishu = (callback?: any) => {
  console.log('开始初始化飞书H5 SDK')

  // 检查是否已经加载过
  if (window.h5sdk) {
    console.log('飞书H5 SDK已存在，直接调用回调')
    typeof callback === 'function' && callback()
    return
  }

  // 动态导入配置
  import('@/config/feishu.config').then(({ getCurrentConfig }) => {
    const config = getCurrentConfig()
    console.log('飞书配置加载成功:', config)

    // 检查是否已经有script标签
    const existingScript = document.querySelector(`script[src="${config.SDK_URL}"]`)
    if (existingScript) {
      console.log('飞书SDK脚本已存在，等待加载完成')
      // 如果脚本已存在，等待一段时间后检查
      setTimeout(() => {
        if (window.h5sdk) {
          console.log('飞书SDK已加载完成')
          typeof callback === 'function' && callback()
        } else {
          console.warn('飞书SDK脚本存在但未正确加载')
          typeof callback === 'function' && callback()
        }
      }, 1000)
      return
    }

    const script = document.createElement('script')
    script.src = config.SDK_URL
    script.async = true

    script.onload = () => {
      console.log('飞书SDK脚本加载完成')

      // 等待SDK完全初始化
      let checkCount = 0
      const maxChecks = 50 // 最多检查5秒

      const checkSDK = () => {
        checkCount++
        console.log(`检查飞书SDK状态 (${checkCount}/${maxChecks})`)

        if (window.h5sdk) {
          console.log('飞书H5 SDK对象已创建')

          // 检查是否有App ID配置
          if (!config.APP_CONFIG.appId) {
            console.warn('飞书App ID未配置，跳过SDK配置，直接调用回调')
            typeof callback === 'function' && callback()
            return
          }

          try {
            console.log('开始配置飞书H5 SDK')
            // 飞书H5 SDK配置
            window.h5sdk.config({
              appId: config.APP_CONFIG.appId,
              timestamp: new Date().getTime(),
              nonceStr: Math.random().toString(36).substring(2, 15),
              signature: '', // 需要后端计算签名
              jsApiList: config.JS_API_LIST,
              onSuccess: (res: any) => {
                console.log('飞书H5 SDK配置成功:', res)
                if (window.h5sdk.ready) {
                  window.h5sdk.ready(() => {
                    console.log('飞书H5 SDK ready回调执行')
                    typeof callback === 'function' && callback()
                  })
                } else {
                  console.log('飞书H5 SDK无ready方法，直接调用回调')
                  typeof callback === 'function' && callback()
                }
              },
              onFail: (res: any) => {
                console.error('飞书H5 SDK配置失败:', res)
                typeof callback === 'function' && callback()
              }
            })
          } catch (error) {
            console.error('飞书H5 SDK配置异常:', error)
            typeof callback === 'function' && callback()
          }
        } else if (checkCount < maxChecks) {
          // 继续检查
          setTimeout(checkSDK, 100)
        } else {
          console.error('飞书H5 SDK加载超时')
          typeof callback === 'function' && callback()
        }
      }

      // 开始检查SDK状态
      checkSDK()
    }

    script.onerror = (error) => {
      console.error('飞书H5 SDK脚本加载失败:', error)
      typeof callback === 'function' && callback()
    }

    console.log('添加飞书SDK脚本到页面')
    document.head.appendChild(script)

  }).catch((error) => {
    console.error('加载飞书配置失败:', error)
    typeof callback === 'function' && callback()
  })
}

// 函数防抖
const debounce = (fn: any, wait?: number) => {
  let timer: any | null = null
  return function () {
    if (timer !== null) {
      clearTimeout(timer)
    }
    timer = setTimeout(fn, wait)
  }
}

// 获取自然周
const createWeeks = (year: number): Array<number> => {
  const d = new Date(year, 0, 1)
  while (d.getDay() != 1) {
    d.setDate(d.getDate() + 1)
  }
  const to = new Date(year + 1, 0, 1)
  let i = 1
  const arr = []
  for (let from = d; from < to; ) {
    from.setDate(from.getDate() + 6)
    if (from < to) {
      from.setDate(from.getDate() + 1)
    } else {
      to.setDate(to.getDate() - 1)
    }
    arr.push(i)
    i++
  }
  return arr
}
// 获取当前第几周
const getYearWeek = (day = new Date().getTime()): number => {
  const d1 = new Date(day)
  const d2 = new Date(day)
  d2.setMonth(0)
  d2.setDate(1)
  const rq = d1.getTime() - d2.getTime()
  const days = Math.ceil(rq / (24 * 60 * 60 * 1000))
  const num = Math.ceil(days / 7)
  return num
}
// 初始化周下拉框
const initWeekSelect = (year: number): Array<number> => {
  let weekSelectOptions = createWeeks(year)
  if (year === new Date().getFullYear() - 2) {
    // 如果是最前边的一年且年份下拉框为三个时，要处理月份选择选两年前的周
    weekSelectOptions = weekSelectOptions.filter((item) => item > getYearWeek())
  }
  return weekSelectOptions
}

/**
 * @description: 千位分割
 * @param {Number} num 要分割的数字
 * @return {String}
 */
const thousandSplit = (num: string) => {
  const reg = /\d{1,3}(?=(\d{3})+$)/g
  if (String(num).includes('.')) {
    const arr = String(num).split('.')
    return (arr[0] + '').replace(reg, '$&,') + '.' + arr[1]
  } else {
    return (num + '').replace(reg, '$&,')
  }
}

/**
 * @description: 处理千位展示的数据
 * @param {Number} data 要处理的数据
 * @param {String} indexUnit 指标单位
 * @return {String} displayData 要展示的值
 */

const dealThousandData = (
  data: string,
  indexUnit: string,
  precisions: string,
  openThousandSplit = true
) => {
  const fixedNum = precisions ? parseInt(precisions) : 2
  if ([null, undefined, 'null', 'undefined'].includes(data)) {
    return `-`
  } else {
    const value = new Decimal(data)
      .mul(
        new Decimal(indexUnit === '%' ? 100 : indexUnit === 'PPM' ? 1000000 : 1)
      )
      .toFixed(fixedNum, Decimal.ROUND_HALF_UP)
    return openThousandSplit ? thousandSplit(value) : value
  }
}

// 循环处理下探数据
const loopData = (arr: any, wd?: string, index = 0): Array<XTComItem> => {
  const datas = datasStore()
  index++
  return arr.map((item: any): XTComItem => {
    const {
      dmId,
      indexId,
      indexName,
      indexDt,
      fullCode,
      indexFrequency,
      indexFrequencyId,
      org,
      orgId,
      businessSegments,
      businessSegmentsId,
      signOrgId,
      signOrg,
      actualValue,
      targetValue,
      targetCompletionRate,
      previousChangeRate,
      contemChangeRate,
      indexUnitId,
      indexSort,
      productAtt1,
      productAtt2,
      productAtt3,
      productAtt4,
      precisions,
      indexTypeId,
      indexNameInd,
      isDelete,
      groupId,
      goDown,
      cmimId,
      pj // 用于拖拽排序标记
    } = item
    const normalWDList = [
      productAtt1,
      productAtt2,
      productAtt3,
      productAtt4
    ].filter((item) => item && !item.includes('指标卡'))
    const wdInCardName = normalWDList
      .filter((item) => item.includes('卡片名称'))
      .map((item) => item.split('-')[2])
    const wdInCardTag = normalWDList
      .filter((item) => item.includes('卡片标签'))
      .map((item) => item.split('-')[2])
    const wdInXT = [productAtt1, productAtt2, productAtt3, productAtt4].filter(
      (item) => item && item.split('-').length === 2
    )

    return {
      label: {
        dmId,
        indexId,
        indexName,
        indexDt,
        fullCode,
        indexFrequency,
        indexFrequencyId,
        org,
        orgId,
        businessSegments,
        businessSegmentsId,
        signOrgId,
        signOrg,
        actualValue: dealThousandData(
          actualValue,
          item.indexUnitId,
          precisions
        ),
        targetValue: dealThousandData(
          targetValue,
          item.indexUnitId,
          precisions
        ),
        targetCompletionRate: targetCompletionRate
          ? `${new Decimal(targetCompletionRate)
              .mul(new Decimal(100))
              .toFixed(2, Decimal.ROUND_HALF_UP)}%`
          : '',
        previousChangeRate,
        isPreviousRate: 'Y',
        isContemRate: 'Y',
        contemChangeRate,
        indexUnitId,
        indexType: indexTypeId,
        indexNameInd,
        displayIndexName: indexNameInd || indexName,
        indexSort,
        normalWDList,
        wdInCardName: wdInCardName.join('-'),
        wdInCardTag,
        groupId,
        cmimId,
        isDelete,
        goDown: goDown ? goDown.split(',') : [],
        wd: wd ? wd + '-' + (wdInXT.length ? wdInXT[0].split('-')[1] : '') : '',
        companyName: datas.companyName,
        label: '同环比恶化,未达标',
        index,
        pj
      },
      index,
      children: loopData(item.list || [], wd, index),
      expand: false
      // expand: index < 3 && (item.list || []).length
    }
  })
}

// 数组转树结构
const arrayToTree = (items: Array<IndexTreeItem>): Array<IndexTreeItem> => {
  const result = [] // 存放结果集
  const itemMap = {} as any
  for (const item of items) {
    const id = item.key
    const pid = item.parentKey || 'x00001'

    if (!itemMap[id]) {
      itemMap[id] = {
        children: []
      }
    }

    itemMap[id] = {
      ...item,
      children: itemMap[id]['children']
    }

    const treeItem = itemMap[id]
    if (pid === 'x00001') {
      result.push(treeItem)
    } else {
      if (!itemMap[pid]) {
        itemMap[pid] = {
          children: []
        }
      }
      itemMap[pid].children.push(treeItem)
    }
  }
  return result
}

/**
 * 树结构数组扁平化
 * @param {*} data 树结构的数组
 * @returns
 */
const treeToArray = (data: any) => {
  return data.reduce((pre: any, cur: any) => {
    const { children = [], list = [], ...item } = cur
    return pre.concat(
      [{ ...item, children, list }],
      treeToArray(children || list)
    )
  }, [])
}

const zhMap = {
  1: '一',
  2: '二',
  3: '三'
} as Record<number, string>

// 创建一个模拟的飞书SDK对象，用于非飞书环境
const createMockFeishuSDK = () => {
  console.log('创建模拟飞书SDK对象')

  window.h5sdk = {
    config: (options: any) => {
      console.log('模拟飞书SDK配置:', options)
      setTimeout(() => {
        if (options.onSuccess) {
          options.onSuccess({ code: 0, message: 'mock success' })
        }
      }, 100)
    },
    ready: (callback: any) => {
      console.log('模拟飞书SDK ready')
      setTimeout(callback, 100)
    }
  }

  window.tt = {
    setNavigationBarTitle: (options: any) => {
      console.log('模拟设置导航栏标题:', options.title)
      if (options.success) options.success()
    },
    navigateBack: (options: any) => {
      console.log('模拟返回操作')
      if (options.success) options.success()
    },
    showToast: (options: any) => {
      console.log('模拟显示Toast:', options.title)
      if (options.success) options.success()
    },
    openChat: (options: any) => {
      console.log('模拟打开聊天:', options)
      if (options.success) options.success()
    },
    getSystemInfo: (options: any) => {
      console.log('模拟获取系统信息')
      if (options.success) {
        options.success({
          platform: 'web',
          version: '1.0.0'
        })
      }
    }
  }
}

export {
  CooieTool,
  getUrlParam,
  buildParams,
  initFeishu,
  createMockFeishuSDK,
  debounce,
  getYearWeek,
  initWeekSelect,
  dealThousandData,
  loopData,
  arrayToTree,
  treeToArray,
  zhMap
}
