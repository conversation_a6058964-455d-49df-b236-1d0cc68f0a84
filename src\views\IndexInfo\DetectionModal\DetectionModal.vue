<script lang="ts" setup>
import type { XTComItem } from '@/config/card.config'
// import { initQing } from '@/utils/util'
import dataStore from '@/stores/datas'
import { storeToRefs } from 'pinia'
import Decimal from 'decimal.js'

const datas = dataStore()
const { isLandscapeInXT } = storeToRefs(datas)

const caretUp = new URL(
  '../../../assets/images/home/<USER>',
  import.meta.url
).href
const caretDown = new URL(
  '../../../assets/images/home/<USER>',
  import.meta.url
).href

const props = defineProps<{
  isOrgXT: boolean
  show: boolean // 展示弹窗
  XTData: XTComItem // 组织下探数据
}>()

const emit = defineEmits(['close', 'nodeClick'])

// 节点点击
const onNodeClick = (e: any, data: XTComItem) => {
  if (data.label.goDown?.length && props.isOrgXT) {
    emit('nodeClick', data)
  }
}

const rotatePage = (): void => {
  // if (!window.qing) {
  //   initQing(() => {
  //     rotatePage()
  //   })
  //   return
  // }
  datas.updateIsLandscapeInXT(!isLandscapeInXT.value)
  // window.qing.call('rotateUI', {
  //   orientation: isLandscapeInXT.value ? 'landscape' : 'portrait'
  // })
}
</script>
<template>
  <div
    v-if="show"
    class="detection-modal"
    :class="[
      isLandscapeInXT ? 'isLandscape' : '',
      props.isOrgXT ? 'isOrgXT' : ''
    ]"
  >
    <vue3-tree-org
      :data="props.XTData"
      :horizontal="!isLandscapeInXT"
      collapsable
      :draggable-on-node="true"
      :toolBar="{
        scale: props.isOrgXT,
        restore: props.isOrgXT,
        expand: false,
        zoom: props.isOrgXT,
        fullscreen: false
      }"
      @on-node-click="onNodeClick"
    >
      <!-- 自定义节点内容 -->
      <template v-slot="{ node }">
        <div
          class="tree-org-node__text node-label tree-org-node-own-style"
          :class="node.label.index === 1 ? 'first' : ''"
        >
          <div class="index-name" v-if="node.label.index === 1">
            {{ node.label.wdInCardName ? node.label.wdInCardName + ' - ' : '' }}
            {{ node.label.displayIndexName }}
          </div>
          <div class="org-name">
            {{ props.isOrgXT ? node.label.org : node.label.wd }}
          </div>
          <template
            v-if="node.label.isDelete === null || node.label.isDelete === 'Y'"
          >
            <div
              class="sjz"
              :style="node.label.isDelete ? 'color: #5f8cc0' : 'color: #a5a5a5'"
            >
              不计算
            </div>
          </template>
          <template v-else>
            <div
              class="sjz"
              :style="{
                color: node.label.targetCompletionRate
                  ? parseFloat(node.label.targetCompletionRate) < 100
                    ? '#0B70FE'
                    : '#F75050'
                  : ''
              }"
            >
              {{ node.label.actualValue }}
              {{ node.label.indexUnitId }}
            </div>
          </template>
          <div class="intro">
            <div>
              目标值 {{ node.label.targetValue }} {{ node.label.indexUnitId }}
            </div>
            <div>完成率 {{ node.label.targetCompletionRate }}</div>
          </div>
          <template v-if="!props.isOrgXT">
            <div class="thb">
              <div>
                同比
                <template v-if="node.label.isContemRate === 'Y'">
                  <span
                    :style="{
                      color: node.label.contemChangeRate
                        ? node.label.indexType === '正向'
                          ? node.label.contemChangeRate.includes('-')
                            ? '#0b70fe'
                            : '#f53f3f'
                          : node.label.contemChangeRate.includes('-')
                          ? '#f53f3f'
                          : '#0b70fe'
                        : '#4e5969'
                    }"
                  >
                    <template v-if="node.label.contemChangeRate">
                      <img
                        :src="
                          node.label.indexType === '正向'
                            ? node.label.contemChangeRate.includes('-')
                              ? caretDown
                              : caretUp
                            : node.label.contemChangeRate.includes('-')
                            ? caretUp
                            : caretDown
                        "
                        alt=""
                      />
                    </template>
                    {{
                      node.label.contemChangeRate
                        ? Math.abs(
                            parseFloat(
                              new Decimal(node.label.contemChangeRate)
                                .mul(new Decimal(100))
                                .toFixed(2, Decimal.ROUND_HALF_UP)
                            )
                          ) + '%'
                        : '-'
                    }}</span
                  >
                </template>
                <template v-else> 不对比 </template>
              </div>
              <div>
                环比
                <template v-if="node.label.isPreviousRate === 'Y'">
                  <span
                    :style="{
                      color: node.label.previousChangeRate
                        ? node.label.indexType === '正向'
                          ? node.label.previousChangeRate.includes('-')
                            ? '#0b70fe'
                            : '#f53f3f'
                          : node.label.previousChangeRate.includes('-')
                          ? '#f53f3f'
                          : '#0b70fe'
                        : '#4e5969'
                    }"
                  >
                    <template v-if="node.label.previousChangeRate">
                      <img
                        :src="
                          node.label.indexType === '正向'
                            ? node.label.previousChangeRate.includes('-')
                              ? caretDown
                              : caretUp
                            : node.label.previousChangeRate.includes('-')
                            ? caretUp
                            : caretDown
                        "
                        alt=""
                      />
                    </template>
                    {{
                      node.label.previousChangeRate
                        ? Math.abs(
                            parseFloat(
                              new Decimal(node.label.previousChangeRate)
                                .mul(new Decimal(100))
                                .toFixed(2, Decimal.ROUND_HALF_UP)
                            )
                          ) + '%'
                        : '-'
                    }}</span
                  >
                </template>
                <template v-else> 不对比 </template>
              </div>
            </div>
          </template>
        </div>
      </template>
    </vue3-tree-org>
    <!-- 操作区 -->
    <template v-if="props.isOrgXT">
      <div class="operate-area">
        <div class="hp" @click.stop="rotatePage">
          <img src="@/assets/images/home/<USER>" alt="" srcset="" />
        </div>
        <div class="exit" @click.stop="emit('close')">
          <img
            src="@/assets/images/index-info/<EMAIL>"
            alt=""
            srcset=""
          />
        </div>
      </div>
    </template>
  </div>
</template>
<style lang="less">
.detection-modal {
  background: #fff;
  z-index: 10;
  .operate-area {
    position: absolute;
    right: 10px;
    bottom: 20px;
    div {
      width: 32px;
      height: 32px;
      border: 1px solid #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      &:not(:last-child) {
        margin-bottom: 6px;
      }
    }
    img {
      display: block;
      width: 16px;
      height: 16px;
    }
  }
}
.wdXT-modal,
.detection-modal.isOrgXT {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  margin-left: -50vw;
  margin-top: -50vh;
}
.detection-modal {
  height: 100%;
  // &.isLandscape {
  //   transform: rotateZ(90deg);
  //   width: 100vh;
  //   height: 100vw;
  //   margin-left: -50vh;
  //   margin-top: -50vw;
  // }
  .zm-tree-handle {
    bottom: 100px;
  }
  .tree-org-node__expand {
    border: none;
    background-image: url('@/assets/images/index-info/<EMAIL>');
    background-position: center center;
    &.expanded {
      background-image: url('@/assets/images/index-info/<EMAIL>');
    }
    .tree-org-node__expand-btn {
      display: none;
    }
  }
  .tree-org-node__content .tree-org-node__inner {
    box-shadow: none;
  }
  .tree-org-node-own-style {
    &.first {
      padding-top: 26px;
      border-color: #13bbad;
      background-color: #f1fffe;
    }
    text-align: left;
    padding: 8px;
    border: 1px solid #c9cdd4;
    min-width: 120px;
    position: relative;
    box-sizing: border-box;
    .org-name {
      height: 20px;
      color: #1d2129;
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 12px;
      line-height: 20px;
      margin-bottom: 4px;
    }
    .sjz {
      color: #4e5969;
      font-size: 14px;
      height: 22px;
      line-height: 22px;
      margin-bottom: 8px;
    }
    .intro {
      font-size: 12px;
      color: #4e5969;
      & > div:not(:last-child) {
        margin-bottom: 8px;
      }
    }
    .index-name {
      position: absolute;
      left: 50%;
      top: 0;
      margin-left: -50%;
      width: 100%;
      height: 20px;
      font-size: 12px;
      color: #fff;
      background-color: #13bbad;
      box-sizing: border-box;
      padding: 0 8px;
      line-height: 20px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
}
</style>
