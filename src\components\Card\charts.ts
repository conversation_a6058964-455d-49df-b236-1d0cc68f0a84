import * as echarts from 'echarts'
import * as _ from 'lodash'
import type { CardItem } from '@/config/card.config'
import { getChartData } from '@/service/home'
import { dealThousandData } from '@/utils/util'
import { ref, reactive } from 'vue'
import Decimal from 'decimal.js'

function CardChart() {
  let kpiCompareChart = reactive<any>({}) // 实例化对象
  const kpiCompareChartOptions = ref<any>({
    // 图表配置
    legend: {
      data: ['同期', '实际', '目标'],
      left: 0,
      bottom: 0,
      itemWidth: 4,
      itemHeight: 4,
      textStyle: {
        color: '#85909C',
        fontSize: 12,
        lineHeight: 20,
        height: 20,
        borderRadius: 0
      },
      padding: 0,
      itemGap: 12,
      icon: 'rect'
    },
    grid: {
      left: '5%',
      right: '5%',
      top: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: [],
      boundaryGap: true,
      axisTick: {
        show: false,
        alignWithLabel: true
      },
      axisLine: {
        lineStyle: {
          color: '#C9CDD4'
        }
      },
      axisLabel: {
        color: '#85909C'
      }
    },

    yAxis: {
      type: 'value',
      axisTick: {
        show: false
      },
      axisLine: {
        show: false
      },
      axisLabel: {
        color: '#85909C',
        formatter: function (value: any) {
          return value
            ? new Decimal(value).toFixed(2, Decimal.ROUND_HALF_UP)
            : ''
        }
        // width: 30,
        // overflow: 'truncate'
      },
      min: function (value: any) {
        return value.min
      },
      max: function (value: any) {
        return value.max
      }
    },
    series: [
      {
        name: '同期',
        type: 'line',
        data: [],
        itemStyle: {
          color: 'rgb(141, 180, 226)'
        },
        symbol: 'circle',
        symbolSize: 6,
        smooth: true
      },
      {
        name: '实际',
        type: 'line',
        data: [],
        itemStyle: {
          color: '#00AAA6'
        },
        label: {
          show: true,
          position: 'top'
        },
        symbol: 'circle',
        symbolSize: 6,
        smooth: true
      },
      {
        name: '目标',
        type: 'line',
        data: [],
        itemStyle: {
          color: 'rgb(255, 192, 0)'
        },
        symbol: 'circle',
        symbolSize: 6,
        smooth: true
      }
    ]
  })
  const cloneKpiCompareChartOptions = ref<any>({}) // 克隆图表配置
  const cardData = ref<any>() // 数据
  const container = ref<any>(null) // container 要渲染的容器
  // 初始化图表
  const initChart = (data: CardItem, dom: null, callback?: any) => {
    cardData.value = data
    container.value = dom
    kpiCompareChart = echarts.init(container.value)
    cloneKpiCompareChartOptions.value = _.cloneDeep(
      kpiCompareChartOptions.value
    )
    getChartDataFun(callback)
  }

  // 获取图表数据
  const getChartDataFun = (callback?: any) => {
    const {
      signOrgId,
      businessSegmentsId,
      indexId,
      fullCode,
      indexDt,
      indexFrequencyId,
      productAtt1Id,
      productAtt2Id,
      productAtt3Id,
      productAtt4Id,
      productAtt5Id,
      productAtt6Id,
      productAtt7Id
    } = cardData.value
    const arr = [
      productAtt1Id,
      productAtt2Id,
      productAtt3Id,
      productAtt4Id,
      productAtt5Id,
      productAtt6Id,
      productAtt7Id
    ]
    arr.forEach((item, index) => {
      arr[index] = item
        ? item.includes('卡片名称') || item.includes('卡片标签')
          ? item
          : null
        : null
    })
    getChartData({
      signOrgId,
      businessSegmentsId,
      indexId,
      fullCode,
      indexDt,
      indexFrequencyId,
      productAtt1Id: arr[0],
      productAtt2Id: arr[1],
      productAtt3Id: arr[2],
      productAtt4Id: arr[3],
      productAtt5Id: arr[4],
      productAtt6Id: arr[5],
      productAtt7Id: arr[6]
    }).then((res) => {
      if (Array.isArray(res) && res.length > 0) {
        res = _.sortBy(res, function (item: any) {
          return item.indexDt
        })
        const xAxis = res.map((item: any) => {
          return item.indexDt
        })
        kpiCompareChartOptions.value.xAxis.data = xAxis
        const baseActualData = res.map((item: any) => {
          return dealThousandData(
            item.actualValue,
            item.indexUnitId,
            item.precisions,
            false
          )
        })
        kpiCompareChartOptions.value.series[1].data = baseActualData
        const targetValueData = res.map((item: any) => {
          return dealThousandData(
            item.targetValue,
            item.indexUnitId,
            item.precisions,
            false
          )
        })
        kpiCompareChartOptions.value.series[2].data = targetValueData
        const contemValueData = res.map((item: any) => {
          return dealThousandData(
            item.contemValue,
            item.indexUnitId,
            item.precisions,
            false
          )
        })
        // this.kpiCompareChartOptions.dataZoom[0].end = 100
        // // 根据数据条数 判断是否显示dataZoom组件
        // if (res.length > 6) {
        //   this.kpiCompareChartOptions.dataZoom[0].show = true
        //   this.kpiCompareChartOptions.grid.bottom = 52
        //   if (res.length > 13) {
        //     this.kpiCompareChartOptions.dataZoom[0].start = 50
        //   } else {
        //     this.kpiCompareChartOptions.dataZoom[0].start = 0
        //   }
        // }

        kpiCompareChartOptions.value.series[0].data = contemValueData
        // this.kpiCompareChartOptions.yAxis.axisLabel.formatter = `{value} ${
        //   res[0].indexUnitId === 'null' ? '' : res[0].indexUnitId
        // }`
        initKpiCompare(callback)
      } else {
        kpiCompareChartOptions.value = _.cloneDeep(
          cloneKpiCompareChartOptions.value
        )
        initKpiCompare(callback)
      }
    })
  }

  // 显示图表
  const initKpiCompare = (callback?: any) => {
    kpiCompareChart.setOption(kpiCompareChartOptions.value)
    typeof callback === 'function' && callback()
  }

  // 销毁并还原配置
  const destory = () => {
    kpiCompareChart = null
    kpiCompareChartOptions.value = _.cloneDeep(
      cloneKpiCompareChartOptions.value
    )
  }

  const resize = () => {
    kpiCompareChart.resize()
  }

  return {
    initChart,
    destory,
    resize
  }
}
// class CardChart {
//   // 实例化对象
//   kpiCompareChart: any = null
//   // 图表配置
//   kpiCompareChartOptions: any = {
//     legend: {
//       data: ['同期', '实际', '目标'],
//       left: 0,
//       bottom: 0,
//       itemWidth: 4,
//       itemHeight: 4,
//       textStyle: {
//         color: '#85909C',
//         fontSize: 12,
//         lineHeight: 20,
//         height: 20,
//         borderRadius: 0
//       },
//       padding: 0,
//       itemGap: 12,
//       icon: 'rect'
//     },
//     grid: {
//       left: '5%',
//       right: '5%',
//       top: '4%',
//       bottom: '15%',
//       containLabel: true
//     },
//     xAxis: {
//       type: 'category',
//       data: [],
//       boundaryGap: true,
//       axisTick: {
//         show: false,
//         alignWithLabel: true
//       },
//       axisLine: {
//         lineStyle: {
//           color: '#C9CDD4'
//         }
//       },
//       axisLabel: {
//         color: '#85909C'
//       }
//     },

//     yAxis: {
//       type: 'value',
//       axisTick: {
//         show: false
//       },
//       axisLine: {
//         show: false
//       },
//       axisLabel: {
//         color: '#85909C',
//         formatter: function (value: any) {
//           return value ? parseFloat(value).toFixed(2) : ''
//         }
//         // width: 30,
//         // overflow: 'truncate'
//       },
//       min: function (value: any) {
//         return value.min
//       },
//       max: function (value: any) {
//         return value.max
//       }
//     },
//     series: [
//       {
//         name: '同期',
//         type: 'line',
//         data: [],
//         itemStyle: {
//           color: 'rgba(255, 206, 140, 1)'
//         },
//         label: {
//           show: false
//         },
//         symbol: 'circle',
//         symbolSize: 6
//       },
//       {
//         name: '实际',
//         type: 'line',
//         data: [],
//         itemStyle: {
//           color: 'rgba(255, 135, 128, 1)'
//         },
//         label: {
//           show: false
//         },
//         symbol: 'circle',
//         symbolSize: 6
//       },
//       {
//         name: '目标',
//         type: 'line',
//         data: [],
//         itemStyle: {
//           color: 'rgba(159, 105, 246, 1)'
//         },
//         label: {
//           show: false
//         },
//         symbol: 'circle',
//         symbolSize: 6
//       }
//     ]
//   }
//   // 克隆图表配置
//   cloneKpiCompareChartOptions = {}
//   data: CardItem // 数据
//   container: HTMLElement // container 要渲染的容器

//   constructor(data: CardItem, container: any) {
//     this.data = data
//     this.container = container
//   }

//   // 初始化图表
//   initChart(callback?: any) {
//     this.kpiCompareChart = echarts.init(this.container)
//     this.cloneKpiCompareChartOptions = _.cloneDeep(this.kpiCompareChartOptions)
//     this.getChartDataFun(callback)
//   }

//   // 获取图表数据

//   // 显示图表
//   initKpiCompare(callback?: any) {
//     this.kpiCompareChart.setOption(this.kpiCompareChartOptions)
//     typeof callback === 'function' && callback()
//   }

//   // 销毁
//   destory() {
//     this.kpiCompareChartOptions = _.cloneDeep(this.cloneKpiCompareChartOptions)
//   }
//   resize() {
//     this.kpiCompareChart.resize()
//   }
// }

export { CardChart }
