<script lang="ts" setup>
import { ref } from 'vue'
import type { CommentItem } from '@/config/card.config'
import userStore from '@/stores/user'
import datasStore from '@/stores/datas'
import { storeToRefs } from 'pinia'
import { submitComment, updateComment, delComment } from '@/service/home'

const user = userStore()
const datas = datasStore()
const { activeCardItem } = storeToRefs(datas)

const emits = defineEmits(['refresh'])

const props = defineProps<{
  commentList: Array<CommentItem>
}>()

const comment = ref() // 评论区ref
const commentShow = ref(false) // 评论弹窗
const message = ref('') // 评论内容
// const offsetTop = ref(0) // 评论区距离顶部距离
const showFixedBtn = ref(true) // 显示fixed底部按钮
const editedItemId = ref() // 编辑的item ID

// 编辑
const edit = (id: number) => {
  editedItemId.value = id
  message.value = props.commentList.filter(
    (item) => item.id === id
  )[0].commentContent
  commentShow.value = true
}

// 删除
const deleteFun = (item: CommentItem) => {
  const postData = {
    ids: [item.id],
    indexId: `${activeCardItem.value.signOrgId}=${activeCardItem.value.businessSegmentsId}=${activeCardItem.value.indexId}`,
    menu_name: `${datas.companyName}核心KPI概览-信鸿轻应用`
  }
  delComment(postData).then(() => {
    emits('refresh')
    closeSheet()
  })
}

// 提交
const submit = () => {
  let fun = editedItemId.value ? updateComment : submitComment
  const postData = {} as Record<string, string | number>
  postData['commentContent'] = message.value
  postData['menu_name'] = `${datas.companyName}核心KPI概览-信鸿轻应用`
  postData[
    'indexId'
  ] = `${activeCardItem.value.signOrgId}=${activeCardItem.value.businessSegmentsId}=${activeCardItem.value.indexId}`
  if (editedItemId.value) {
    const originData = props.commentList.filter(
      (item) => item.id === editedItemId.value
    )[0]
    postData['id'] = originData.id
  } else {
    postData['org'] = activeCardItem.value.org
    postData['indexDt'] = activeCardItem.value.indexDt
    postData['frequency'] = activeCardItem.value.indexFrequency
  }
  fun(postData).then(() => {
    emits('refresh')
    closeSheet()
  })
}

// 关闭面板
const closeSheet = () => {
  commentShow.value = false
  message.value = ''
  editedItemId.value = ''
}

// const onScroll = () => {
//   console.log(document.documentElement.scrollTop || document.body.scrollTop)
// }

// onMounted(() => {
//   nextTick(() => {
//     offsetTop.value = comment.value.offsetTop
//     document.addEventListener('scroll', onScroll)
//   })
// })

// onUnmounted(() => {
//   document.removeEventListener('scroll', onScroll)
// })
</script>
<template>
  <div class="comment-list" ref="comment">
    <div class="top">
      <div class="title">
        指标评论 <span>{{ props.commentList.length }}</span>
      </div>
    </div>
    <template v-if="props.commentList.length === 0">
      <van-empty description="暂无原因分析" />
    </template>
    <template v-else>
      <div class="list">
        <div class="item" v-for="item in props.commentList" :key="item.id">
          <div class="_top">
            <div class="name">{{ item.commentPeopleName }}</div>
            <div class="intro">{{ item.org }}/{{ item.indexDt }}</div>
          </div>
          <div class="_content">
            {{ item.commentContent }}
          </div>
          <div class="_bottom">
            <div class="time">{{ item.createdDate }}</div>
            <div class="op">
              <template v-if="item.createdBy === user.nowLoginUser">
                <div class="_btn" @click="edit(item.id)">
                  <img
                    src="@/assets/images/home/<USER>"
                    alt=""
                    srcset=""
                  />
                  编辑
                </div>
                <div class="_btn" @click="deleteFun(item)">
                  <img
                    src="@/assets/images/home/<USER>"
                    alt=""
                    srcset=""
                  />
                  删除
                </div>
              </template>
              <!-- <template v-else>
              <div class="_btn">
                <img
                  src="@/assets/images/home/<USER>"
                  alt=""
                  srcset=""
                />
                回复
              </div>
            </template> -->
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
  <div class="comment-fixed-area" v-if="showFixedBtn">
    <div class="btn" @click="commentShow = true">
      发表原因分析，参与指标讨论
    </div>
  </div>
  <van-action-sheet
    class="comment-sheet"
    v-model:show="commentShow"
    title="评论"
    @close="closeSheet"
  >
    <div class="content">
      <van-field
        v-model="message"
        rows="2"
        autosize
        label=""
        type="textarea"
        maxlength="100"
        placeholder="请输入内容"
        show-word-limit
      />
    </div>
    <div class="bottom">
      <div class="cancel" @click="closeSheet">取消</div>
      <div class="submit" @click="submit">提交</div>
    </div>
  </van-action-sheet>
</template>
<style lang="less">
.comment-list {
  position: relative;
  z-index: 2;
  width: 351px;
  border-radius: 8px;
  background: #ffffff;
  margin: 0 auto;
  box-sizing: border-box;
  margin-bottom: 8px;
  padding: 0;
  padding-bottom: 12px;
  .top {
    padding: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    .title {
      font-size: 14px;
      color: #1d2129;
      height: 22px;
      line-height: 22px;
      font-weight: 600;
      span {
        display: inline-block;
        margin-left: 6px;
        color: #98a0ab;
      }
    }
  }
  .list {
    padding: 0 16px;
    box-sizing: border-box;
    .item {
      width: 100%;
      background: #f2f3f5;
      box-sizing: border-box;
      padding: 12px;
      margin-bottom: 8px;
      ._top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        white-space: nowrap;
        margin-bottom: 8px;
        .name {
          height: 22px;
          color: #1d2129;
          font-weight: bold;
          font-size: 14px;
          line-height: 22px;
        }
        .intro {
          height: 20px;
          color: #4e5969;
          font-size: 12px;
          line-height: 20px;
        }
      }
      ._content {
        color: #1d2129;
        font-size: 14px;
        line-height: 22px;
        margin-bottom: 8px;
      }
      ._bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .time {
          height: 20px;
          color: #86909c;
          font-size: 12px;
          line-height: 20px;
        }
        .op {
          display: flex;
          align-items: center;
          ._btn {
            display: flex;
            align-items: center;
            margin-left: 12px;
            img {
              display: block;
              width: 16px;
              height: 16px;
              margin-right: 4px;
            }
            height: 20px;
            color: #86909c;
            font-size: 12px;
            line-height: 20px;
          }
        }
      }
    }
  }
}
.comment-fixed-area {
  position: fixed;
  bottom: 0;
  left: 50%;
  margin-left: -50%;
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  z-index: 2;
  align-items: center;
  justify-content: center;
  .btn {
    width: calc(100% - 32px);
    height: 32px;
    border-radius: 16px;
    line-height: 32px;
    box-sizing: border-box;
    padding: 0 16px;
    font-size: 12px;
    color: #323233;
    background: #f7f8fa;
  }
}
.comment-sheet {
  .content {
    .van-field {
      width: 90%;
      margin: 0 auto;
      background-color: #f2f3f5;
      padding: 8px;
      border-radius: 4px;
    }
  }
  .bottom {
    height: 76px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    padding: 0 12px;
    & > div {
      &:first-child {
        margin-right: 12px;
        border: 1px solid #00aaa6;
        color: #00aaa6;
        background-color: #fff;
      }
      text-align: center;
      width: calc(100% / 2);
      height: 38px;
      box-sizing: border-box;
      line-height: 38px;
      color: #fff;
      background-color: #00aaa6;
      font-size: 14px;
    }
  }
}
</style>
