/**
 * 飞书H5 SDK工具函数
 * 用于封装飞书相关的API调用
 */

// 飞书API调用的通用错误处理
const handleFeishuError = (error: any, apiName: string) => {
  console.error(`飞书API ${apiName} 调用失败:`, error)
}

// 检查飞书SDK是否可用
const checkFeishuSDK = (): boolean => {
  if (!window.h5sdk) {
    console.warn('飞书H5 SDK未加载')
    return false
  }
  if (!window.tt) {
    console.warn('飞书tt对象未初始化')
    return false
  }
  return true
}

// 设置导航栏标题
export const setNavigationBarTitle = (title: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (!checkFeishuSDK()) {
      reject(new Error('飞书SDK未初始化'))
      return
    }

    window.h5sdk.ready(() => {
      window.tt.setNavigationBarTitle({
        title,
        success: () => {
          console.log('设置标题成功:', title)
          resolve()
        },
        fail: (err: any) => {
          handleFeishuError(err, 'setNavigationBarTitle')
          reject(err)
        }
      })
    })
  })
}

// 显示Toast消息
export const showToast = (title: string, icon: 'success' | 'error' | 'loading' | 'none' = 'none'): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (!checkFeishuSDK()) {
      reject(new Error('飞书SDK未初始化'))
      return
    }

    window.h5sdk.ready(() => {
      window.tt.showToast({
        title,
        icon,
        success: () => {
          resolve()
        },
        fail: (err: any) => {
          handleFeishuError(err, 'showToast')
          reject(err)
        }
      })
    })
  })
}

// 返回上一页
export const navigateBack = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (!checkFeishuSDK()) {
      reject(new Error('飞书SDK未初始化'))
      return
    }

    window.h5sdk.ready(() => {
      window.tt.navigateBack({
        success: () => {
          console.log('返回成功')
          resolve()
        },
        fail: (err: any) => {
          handleFeishuError(err, 'navigateBack')
          // 如果返回失败，尝试退出小程序
          if (window.tt.exitMiniProgram) {
            window.tt.exitMiniProgram()
          }
          reject(err)
        }
      })
    })
  })
}

// 打开聊天窗口
export const openChat = (chatId: string, message?: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (!checkFeishuSDK()) {
      reject(new Error('飞书SDK未初始化'))
      return
    }

    window.h5sdk.ready(() => {
      const params: any = {
        chatId,
        success: () => {
          console.log('打开聊天窗口成功')
          resolve()
        },
        fail: (err: any) => {
          handleFeishuError(err, 'openChat')
          reject(err)
        }
      }

      if (message) {
        params.message = message
      }

      window.tt.openChat(params)
    })
  })
}

// 获取系统信息
export const getSystemInfo = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    if (!checkFeishuSDK()) {
      reject(new Error('飞书SDK未初始化'))
      return
    }

    window.h5sdk.ready(() => {
      window.tt.getSystemInfo({
        success: (res: any) => {
          console.log('获取系统信息成功:', res)
          resolve(res)
        },
        fail: (err: any) => {
          handleFeishuError(err, 'getSystemInfo')
          reject(err)
        }
      })
    })
  })
}

// 设置剪贴板内容
export const setClipboardData = (data: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (!checkFeishuSDK()) {
      reject(new Error('飞书SDK未初始化'))
      return
    }

    window.h5sdk.ready(() => {
      window.tt.setClipboardData({
        data,
        success: () => {
          console.log('设置剪贴板成功')
          resolve()
        },
        fail: (err: any) => {
          handleFeishuError(err, 'setClipboardData')
          reject(err)
        }
      })
    })
  })
}

// 获取剪贴板内容
export const getClipboardData = (): Promise<string> => {
  return new Promise((resolve, reject) => {
    if (!checkFeishuSDK()) {
      reject(new Error('飞书SDK未初始化'))
      return
    }

    window.h5sdk.ready(() => {
      window.tt.getClipboardData({
        success: (res: any) => {
          console.log('获取剪贴板成功:', res.data)
          resolve(res.data)
        },
        fail: (err: any) => {
          handleFeishuError(err, 'getClipboardData')
          reject(err)
        }
      })
    })
  })
}

// 显示模态对话框
export const showModal = (title: string, content: string): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    if (!checkFeishuSDK()) {
      reject(new Error('飞书SDK未初始化'))
      return
    }

    window.h5sdk.ready(() => {
      window.tt.showModal({
        title,
        content,
        success: (res: any) => {
          resolve(res.confirm)
        },
        fail: (err: any) => {
          handleFeishuError(err, 'showModal')
          reject(err)
        }
      })
    })
  })
}

// 预览图片
export const previewImage = (urls: string[], current?: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (!checkFeishuSDK()) {
      reject(new Error('飞书SDK未初始化'))
      return
    }

    window.h5sdk.ready(() => {
      const params: any = {
        urls,
        success: () => {
          resolve()
        },
        fail: (err: any) => {
          handleFeishuError(err, 'previewImage')
          reject(err)
        }
      }

      if (current) {
        params.current = current
      }

      window.tt.previewImage(params)
    })
  })
}

// 分享内容
export const share = (title: string, desc?: string, link?: string, imgUrl?: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (!checkFeishuSDK()) {
      reject(new Error('飞书SDK未初始化'))
      return
    }

    window.h5sdk.ready(() => {
      const params: any = {
        title,
        success: () => {
          resolve()
        },
        fail: (err: any) => {
          handleFeishuError(err, 'share')
          reject(err)
        }
      }

      if (desc) params.desc = desc
      if (link) params.link = link
      if (imgUrl) params.imgUrl = imgUrl

      window.tt.share(params)
    })
  })
}
