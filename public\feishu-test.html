<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞书SDK测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>飞书H5 SDK测试页面</h1>
    
    <div id="status" class="status warning">
        状态：正在检测飞书环境...
    </div>
    
    <div>
        <button onclick="loadFeishuSDK()">加载飞书SDK</button>
        <button onclick="testFeishuAPI()">测试飞书API</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <h3>日志输出：</h3>
    <div id="log" class="log"></div>
    
    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(message, type = 'warning') {
            statusElement.textContent = `状态：${message}`;
            statusElement.className = `status ${type}`;
        }
        
        function clearLog() {
            logElement.textContent = '';
        }
        
        function checkFeishuEnvironment() {
            log('检查飞书环境...');
            
            // 检查User Agent
            const userAgent = navigator.userAgent;
            log(`User Agent: ${userAgent}`);
            
            if (userAgent.includes('Lark') || userAgent.includes('Feishu')) {
                log('检测到飞书客户端环境', 'success');
                updateStatus('在飞书客户端中', 'success');
                return true;
            } else {
                log('未检测到飞书客户端环境', 'warning');
                updateStatus('不在飞书客户端中', 'warning');
                return false;
            }
        }
        
        function loadFeishuSDK() {
            log('开始加载飞书H5 SDK...');
            
            // 检查是否已经加载
            if (window.h5sdk) {
                log('飞书SDK已存在');
                updateStatus('飞书SDK已加载', 'success');
                return;
            }
            
            const script = document.createElement('script');
            script.src = 'https://lf1-cdn-tos.bytegoofy.com/goofy/lark/op/h5-js-sdk-1.5.16.js';
            script.async = true;
            
            script.onload = () => {
                log('飞书SDK脚本加载完成');
                
                // 检查SDK对象
                setTimeout(() => {
                    if (window.h5sdk) {
                        log('飞书H5 SDK对象创建成功');
                        log(`SDK对象类型: ${typeof window.h5sdk}`);
                        log(`SDK对象方法: ${Object.keys(window.h5sdk).join(', ')}`);
                        updateStatus('飞书SDK加载成功', 'success');
                        
                        // 尝试配置SDK
                        try {
                            window.h5sdk.config({
                                appId: 'test_app_id',
                                timestamp: Date.now(),
                                nonceStr: 'test_nonce',
                                signature: 'test_signature',
                                jsApiList: ['biz.navigation.setTitle'],
                                onSuccess: (res) => {
                                    log(`SDK配置成功: ${JSON.stringify(res)}`);
                                },
                                onFail: (res) => {
                                    log(`SDK配置失败: ${JSON.stringify(res)}`);
                                }
                            });
                        } catch (error) {
                            log(`SDK配置异常: ${error.message}`);
                        }
                    } else {
                        log('飞书H5 SDK对象未创建');
                        updateStatus('飞书SDK加载失败', 'error');
                    }
                }, 500);
            };
            
            script.onerror = (error) => {
                log(`飞书SDK脚本加载失败: ${error}`);
                updateStatus('飞书SDK脚本加载失败', 'error');
            };
            
            document.head.appendChild(script);
            log('飞书SDK脚本已添加到页面');
        }
        
        function testFeishuAPI() {
            log('测试飞书API...');
            
            if (!window.h5sdk) {
                log('飞书SDK未加载，无法测试API');
                return;
            }
            
            if (!window.tt) {
                log('飞书tt对象不存在，无法测试API');
                return;
            }
            
            // 测试设置标题
            try {
                window.tt.setNavigationBarTitle({
                    title: '测试标题',
                    success: () => {
                        log('设置标题成功');
                    },
                    fail: (error) => {
                        log(`设置标题失败: ${JSON.stringify(error)}`);
                    }
                });
            } catch (error) {
                log(`设置标题异常: ${error.message}`);
            }
            
            // 测试显示Toast
            try {
                window.tt.showToast({
                    title: '测试Toast',
                    success: () => {
                        log('显示Toast成功');
                    },
                    fail: (error) => {
                        log(`显示Toast失败: ${JSON.stringify(error)}`);
                    }
                });
            } catch (error) {
                log(`显示Toast异常: ${error.message}`);
            }
        }
        
        // 页面加载时自动检查环境
        window.onload = () => {
            log('页面加载完成');
            checkFeishuEnvironment();
            
            // 如果在飞书环境中，自动加载SDK
            if (checkFeishuEnvironment()) {
                setTimeout(() => {
                    loadFeishuSDK();
                }, 1000);
            }
        };
        
        // 监听全局错误
        window.onerror = (message, source, lineno, colno, error) => {
            log(`全局错误: ${message} at ${source}:${lineno}:${colno}`);
        };
    </script>
</body>
</html>
