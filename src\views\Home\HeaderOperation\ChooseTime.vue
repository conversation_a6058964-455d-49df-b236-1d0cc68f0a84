<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import datasStore from '@/stores/datas'
import { storeToRefs } from 'pinia'
import { initWeekSelect } from '@/utils/util'

const datas = datasStore()
const { chooseTime, searchForm } = storeToRefs(datas)

const currentDate = computed(() => searchForm.value.time.split('-')) // 日期

interface ColunmsItem {
  text: string
  value: string
  children?: Array<ColunmsItem>
}

const columns = ref<Array<ColunmsItem>>([]) // 周选择两列选择项

// date-picker格式化显示
const formatter = (type: string, option: any) => {
  if (type === 'year') {
    option.text += '年'
  }
  if (type === 'month') {
    option.text += '月'
  }
  if (type === 'day') {
    option.text += '日'
  }
  return option
}

// 初始化周选择项
const initColumns = () => {
  let c = []
  // 两年前的年份不允许选择
  let beginningYear = new Date().getFullYear() - 2
  // 如果当前月份是12月则，年份选择扣除一年
  if (new Date().getMonth() + 1 === 12) {
    beginningYear++
  }
  for (let i = beginningYear; i <= new Date().getFullYear(); i++) {
    c.unshift(i)
  }
  columns.value = c.map((item: number): ColunmsItem => {
    return {
      text: `${item}年`,
      value: `${item}`,
      children: initWeekSelect(item).map((w) => {
        return {
          text: `${w < 10 ? '0' + w : w}周`,
          value: `${w < 10 ? '0' + w : w}`
        }
      })
    }
  })
}

onMounted(() => {
  initColumns()
})

// 点击确定
const confirm = (value: any) => {
  const indexDt = value.selectedValues.join('-')
  datas.updateSearchForm({ ...searchForm.value, time: indexDt })
  datas.updateChooseTime(false)
}
</script>
<template>
  <van-popup
    position="bottom"
    round
    v-model:show="chooseTime"
    style="height: 318px"
  >
    <van-date-picker
      v-show="['month', 'day'].includes(searchForm.timeType)"
      v-model="currentDate"
      @cancel="datas.updateChooseTime(false)"
      @confirm="confirm"
      :formatter="formatter"
      :columnsType="
        searchForm.timeType === 'month'
          ? ['year', 'month']
          : ['year', 'month', 'day']
      "
      :title="searchForm.timeType === 'month' ? '选择年月' : '选择年月日'"
    />
    <van-picker
      v-model="currentDate"
      v-show="searchForm.timeType === 'week'"
      ref="weekPicker"
      show-toolbar
      title="选择年周"
      :columns="columns"
      @confirm="confirm"
      @cancel="datas.updateChooseTime(false)"
    />
  </van-popup>
</template>
