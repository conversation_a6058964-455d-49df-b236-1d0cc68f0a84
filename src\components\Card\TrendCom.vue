<script lang="ts" setup>
import type { CardItem } from '@/config/card.config'
import { ref, nextTick, onMounted, onUnmounted } from 'vue'
import { CardChart } from './charts'

const props = defineProps<{
  data: CardItem
}>()

const landscape = ref(false)
const kpiCompare = ref()
const chart = CardChart()
const loading = ref(true)

// 横屏点击
const hpClick = () => {
  landscape.value = !landscape.value
  nextTick(() => {
    chart.resize()
  })
}

onMounted(() => {
  nextTick(() => {
    chart.initChart(props.data, kpiCompare.value, () => {
      loading.value = false
    })
  })
})
onUnmounted(() => {
  chart.destory()
})
</script>
<template>
  <div class="myChart-box" :class="[landscape ? 'isLandscape' : '']">
    <div style="height: 100%">
      <template v-if="loading">
        <van-loading
          style="
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            z-index: 5;
            background: #fff;
          "
          color="#13bbad"
        />
      </template>
      <div class="hp" @click.stop="hpClick">
        <img src="@/assets/images/home/<USER>" alt="" srcset="" />
        {{ !landscape ? '横屏展示' : '还原' }}
      </div>
      <div class="myChart" ref="kpiCompare"></div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.myChart-box {
  width: 100%;
  height: 170px;
  position: relative;
  background-color: #fff;
  &.isLandscape {
    position: fixed;
    top: 50%;
    left: 50%;
    width: 100vh;
    height: 100vw;
    transform: rotateZ(90deg);
    z-index: 99;
    margin-left: -50vh;
    margin-top: -50vw;
    box-sizing: border-box;
    padding: 10px;
    .hp {
      bottom: 11px;
      right: 10px;
    }
  }
  .myChart {
    height: 100%;
    width: 100%;
    position: relative;
    z-index: 1;
  }
  .hp {
    z-index: 2;
    display: flex;
    align-items: center;
    position: absolute;
    right: 0;
    bottom: 1px;
    font-size: 12px;
    height: 20px;
    line-height: 20px;
    color: #85909c;
    img {
      display: block;
      width: 16px;
      height: 16px;
      margin-right: 2px;
    }
  }
}
</style>
