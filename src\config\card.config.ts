interface CardItem {
  dmId: string | undefined | null
  indexId: string
  indexName: string
  indexDt: string
  fullCode: string
  indexFrequency: string
  indexFrequencyId: string
  org: string
  orgId: string
  businessSegments: string
  businessSegmentsId: string
  signOrgId: string
  signOrg: string
  actualValue: string
  targetValue: string
  targetCompletionRate: string
  previousChangeRate: string
  isPreviousRate: string
  isContemRate: string
  contemChangeRate: string
  indexUnitId: string
  indexType: string
  indexNameInd: string
  displayIndexName: string // indexNameInd || indexName
  productAtt1Id?: string | undefined | null
  productAtt2Id?: string | undefined | null
  productAtt3Id?: string | undefined | null
  productAtt4Id?: string | undefined | null
  productAtt5Id?: string | undefined | null
  productAtt6Id?: string | undefined | null
  productAtt7Id?: string | undefined | null
  indexSort: number | undefined | null
  normalWDList: Array<string>
  wdInCardName: string
  wdInCardTag: Array<string>
  cmimId: string | undefined | null
  companyName: string
  label: string
  pj: string
  recommend?: boolean
}

// 因为vue3-org-tree自定义节点内容只能通过node.label读取
// XTItem集成CardItem
interface XTItem extends CardItem {
  groupId?: string | undefined | null
  isDelete?: string | undefined | null
  goDown: Array<string>
  wd?: string
  index: number // 层级
}
// XTComItem是vue3-org-tree需要的数据类型
interface XTComItem {
  label: XTItem
  children: Array<XTComItem>
  index: number // 层级
  expand: boolean
}

// 指标卡树返回数据结构
interface IndexTreeItem {
  title: string // 版块名||指标名||组织名
  key: string
  parentKey: string
  fullCode: string | null
  children: Array<IndexTreeItem>
  // childrenCopy?: Array<IndexTreeItem>
  checkedNums?: number // 版块上的选中的指标的角标
  isChecked?: boolean // 指标上是否被选中
  checkedStatus?: string // 组织上选中状态 complete/incomplete/half
}

// 评论区结构
interface CommentItem {
  isEdit?: boolean
  createdBy: string
  commentContent: string
  commentPeopleName: string
  indexDt: string
  org: string
  id: number
  createdDate: string
}

export type { CardItem, XTItem, XTComItem, IndexTreeItem, CommentItem }
